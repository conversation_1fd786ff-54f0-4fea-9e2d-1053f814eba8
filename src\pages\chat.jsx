import React, { useState } from "react";

export default function ChatEducativo() {
  const [mensajes, setMensajes] = useState([]);
  const [input, setInput] = useState("");

  const enviarMensaje = () => {
    if (input.trim() === "") return;

    const nuevoMensaje = { texto: input, autor: "tú" };
    setMensajes([...mensajes, nuevoMensaje]);
    setInput("");

    setTimeout(() => {
      const respuesta = {
        texto: "✨ Germayori: Recuerda enfocarte en la liquidez, no en el impulso. ¿Qué temporalidad estás usando?",
        autor: "germayori",
      };
      setMensajes((mensajesActuales) => [...mensajesActuales, respuesta]);
    }, 1000);
  };

  return (
    <div className="min-h-screen bg-gray-900 text-white p-6">
      <h1 className="text-3xl font-bold mb-4 text-center">💬 Chat Educativo Germayori</h1>

      <div className="max-w-2xl mx-auto bg-gray-800 rounded-lg p-4 shadow-lg">
        <div className="h-80 overflow-y-auto space-y-2 mb-4 bg-gray-700 p-2 rounded">
          {mensajes.map((msg, index) => (
            <div
              key={index}
              className={`p-2 rounded ${
                msg.autor === "germayori" ? "bg-indigo-600 text-left" : "bg-green-600 text-right"
              }`}
            >
              {msg.texto}
            </div>
          ))}
        </div>

        <div className="flex gap-2">
          <input
            value={input}
            onChange={(e) => setInput(e.target.value)}
            placeholder="Escribe tu pregunta..."
            className="flex-1 p-2 rounded text-black"
          />
          <button
            onClick={enviarMensaje}
            className="bg-orange-500 hover:bg-orange-600 text-white px-4 py-2 rounded"
          >
            Enviar
          </button>
        </div>
      </div>
    </div>
  );
}
