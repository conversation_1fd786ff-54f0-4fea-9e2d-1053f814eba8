// Archivo: src/pages/dashboard.jsx

import Head from 'next/head';
import { useState, useEffect } from 'react';
import { useRouter } from 'next/router';

export default function Dashboard() {
  const [seccionActiva, setSeccionActiva] = useState('inicio');
  const [usuario, setUsuario] = useState(null);
  const [cargando, setCargando] = useState(true);
  const router = useRouter();

  // Verificar autenticación al cargar
  useEffect(() => {
    const verificarAuth = () => {
      const usuarioGuardado = localStorage.getItem('usuario_germayori');

      if (!usuarioGuardado) {
        router.push('/login');
        return;
      }

      try {
        const datosUsuario = JSON.parse(usuarioGuardado);

        // Verificar si la suscripción no ha expirado
        const fechaActual = new Date();
        const fechaExpiracion = new Date(datosUsuario.fechaExpiracion);

        if (fechaActual > fechaExpiracion) {
          localStorage.removeItem('usuario_germayori');
          alert('Tu suscripción ha expirado. Renueva tu acceso.');
          router.push('/login');
          return;
        }

        setUsuario(datosUsuario);
      } catch (error) {
        console.error('Error al verificar usuario:', error);
        localStorage.removeItem('usuario_germayori');
        router.push('/login');
        return;
      }

      setCargando(false);
    };

    verificarAuth();
  }, [router]);

  // Función para cerrar sesión
  const cerrarSesion = () => {
    localStorage.removeItem('usuario_germayori');
    router.push('/login');
  };

  // Mostrar loading mientras verifica
  if (cargando) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-blue-900 to-purple-900">
        <div className="text-center text-white">
          <div className="animate-spin rounded-full h-16 w-16 border-b-2 border-orange-500 mx-auto mb-4"></div>
          <p className="text-xl">Verificando acceso...</p>
        </div>
      </div>
    );
  }

  // Si no hay usuario, no mostrar nada (se redirige)
  if (!usuario) {
    return null;
  }

  return (
    <div className="flex h-screen w-full text-white relative" style={{
      background: `
        linear-gradient(135deg, rgba(30, 58, 138, 0.3), rgba(67, 56, 202, 0.3)),
        radial-gradient(circle at 20% 20%, rgba(255, 165, 0, 0.1) 0%, transparent 50%),
        radial-gradient(circle at 80% 20%, rgba(255, 69, 0, 0.1) 0%, transparent 50%),
        radial-gradient(circle at 50% 80%, rgba(0, 255, 255, 0.05) 0%, transparent 50%),
        linear-gradient(45deg, #1e3a8a 0%, #3730a3 50%, #1e1b4b 100%)
      `,
      filter: 'brightness(1.4) contrast(1.3) saturate(1.5)'
    }}>
      {/* Sidebar */}
      <aside className="w-60 bg-blue-950 p-4 flex flex-col">
        <div className="mb-6">
          <h1 className="text-xl font-bold">🚀 GERMAYORI</h1>
          <div className="flex justify-center my-3">
            <img
              src="/logo.png"
              alt="GERMAYORI Logo"
              className="w-16 h-16 rounded-lg shadow-lg"
            />
          </div>
          <p className="text-sm">👤 {usuario?.nombre || 'Usuario'}</p>
          <p className="text-yellow-400 text-xs">⭐ {usuario?.plan || 'PREMIUM'}</p>
          <p className="text-green-400 text-xs">
            ✅ Activo hasta: {usuario?.fechaExpiracion ? new Date(usuario.fechaExpiracion).toLocaleDateString() : 'N/A'}
          </p>

          {/* Botón Perfil */}
          <button
            onClick={() => setSeccionActiva('perfil')}
            className={`w-full mt-3 p-2 rounded-lg text-sm font-semibold transition-all ${
              seccionActiva === 'perfil'
                ? 'bg-orange-500 text-white'
                : 'bg-blue-800 hover:bg-blue-700 text-gray-300 hover:text-white'
            }`}
          >
            👤 Mi Perfil
          </button>

          {/* Botón Cerrar Sesión */}
          <button
            onClick={cerrarSesion}
            className="w-full mt-2 p-2 rounded-lg text-sm font-semibold transition-all bg-red-600 hover:bg-red-700 text-white"
          >
            🚪 Cerrar Sesión
          </button>
        </div>
        <nav className="space-y-2 text-sm">
          <button
            onClick={() => setSeccionActiva('inicio')}
            className={`block w-full text-left hover:text-cyan-300 ${seccionActiva === 'inicio' ? 'text-cyan-300' : ''}`}
          >
            🏠 Inicio
          </button>
          <button
            onClick={() => setSeccionActiva('chat')}
            className={`block w-full text-left hover:text-cyan-300 ${seccionActiva === 'chat' ? 'text-cyan-300' : ''}`}
          >
            💬 Chat Educativo
          </button>
          <button
            onClick={() => setSeccionActiva('trading')}
            className={`block w-full text-left hover:text-cyan-300 ${seccionActiva === 'trading' ? 'text-cyan-300' : ''}`}
          >
            📉 Trading en Vivo
          </button>
          <button
            onClick={() => setSeccionActiva('calculadora')}
            className={`block w-full text-left hover:text-cyan-300 ${seccionActiva === 'calculadora' ? 'text-cyan-300' : ''}`}
          >
            🧮 Calculadora
          </button>
          <button
            onClick={() => setSeccionActiva('noticias')}
            className={`block w-full text-left hover:text-cyan-300 ${seccionActiva === 'noticias' ? 'text-cyan-300' : ''}`}
          >
            📰 Noticias
          </button>
          <button
            onClick={() => setSeccionActiva('senales')}
            className={`block w-full text-left hover:text-cyan-300 ${seccionActiva === 'senales' ? 'text-cyan-300' : ''}`}
          >
            📡 Señales
          </button>
          <button
            onClick={() => setSeccionActiva('notificaciones')}
            className={`block w-full text-left hover:text-cyan-300 ${seccionActiva === 'notificaciones' ? 'text-cyan-300' : ''}`}
          >
            🔔 Notificaciones
          </button>
          <button
            onClick={() => setSeccionActiva('alertas')}
            className={`block w-full text-left hover:text-cyan-300 ${seccionActiva === 'alertas' ? 'text-cyan-300' : ''}`}
          >
            ⚠️ Alertas Mercado
          </button>
          <button
            onClick={() => setSeccionActiva('tradingview')}
            className={`block w-full text-left hover:text-cyan-300 ${seccionActiva === 'tradingview' ? 'text-cyan-300' : ''}`}
          >
            📉 TradingView
          </button>
          <p className="mt-4 text-xs text-gray-400">🎓 EDUCACIÓN GERMAYORI</p>
          <button
            onClick={() => setSeccionActiva('videos')}
            className={`block w-full text-left hover:text-cyan-300 ${seccionActiva === 'videos' ? 'text-cyan-300' : ''}`}
          >
            📘 Videos Educativos <span className="bg-yellow-500 text-black rounded px-2 text-xs ml-1">PREMIUM</span>
          </button>
          <button
            onClick={() => setSeccionActiva('mentoria')}
            className={`block w-full text-left hover:text-cyan-300 ${seccionActiva === 'mentoria' ? 'text-cyan-300' : ''}`}
          >
            🎓 Mentoría Directa <span className="bg-pink-500 text-white rounded px-2 text-xs ml-1">VIP</span>
          </button>
        </nav>
      </aside>

      {/* Main Content */}
      <main className="flex-1 p-8 overflow-auto">
        <Head>
          <title>Dashboard Germayori</title>
        </Head>

        {seccionActiva === 'inicio' && <SeccionInicio setSeccionActiva={setSeccionActiva} />}
        {seccionActiva === 'perfil' && <SeccionPerfil usuario={usuario} cerrarSesion={cerrarSesion} />}
        {seccionActiva === 'senales' && <SeccionSenales />}
        {seccionActiva === 'chat' && <SeccionChat />}
        {seccionActiva === 'trading' && <SeccionTrading />}
        {seccionActiva === 'calculadora' && <SeccionCalculadora />}
        {seccionActiva === 'noticias' && <SeccionNoticias />}
        {seccionActiva === 'notificaciones' && <SeccionNotificaciones />}
        {seccionActiva === 'alertas' && <SeccionAlertas />}
        {seccionActiva === 'tradingview' && <SeccionTradingView />}
        {seccionActiva === 'videos' && <SeccionVideos />}
        {seccionActiva === 'mentoria' && <SeccionMentoria />}
      </main>
    </div>
  );
}

// Componente Perfil de Usuario
function SeccionPerfil({ usuario: usuarioAuth, cerrarSesion }) {
  // Datos del usuario autenticado + datos simulados de trading
  const usuario = {
    nombre: usuarioAuth?.nombre || "Usuario GERMAYORI",
    email: usuarioAuth?.correo || "<EMAIL>",
    plan: usuarioAuth?.plan || "PREMIUM",
    fechaRegistro: usuarioAuth?.fechaRegistro ? new Date(usuarioAuth.fechaRegistro).toLocaleDateString() : "15 Dic 2024",
    fechaExpiracion: usuarioAuth?.fechaExpiracion ? new Date(usuarioAuth.fechaExpiracion).toLocaleDateString() : "N/A",
    ultimoAcceso: "Hoy " + new Date().toLocaleTimeString(),
    estado: usuarioAuth?.estado || "activo",
    // Datos simulados de trading (después se pueden obtener de MongoDB)
    operacionesTotales: 47,
    operacionesGanadas: 32,
    operacionesPerdidas: 15,
    winRate: 68,
    pipsGanados: 1250,
    balanceInicial: 1000,
    balanceActual: 1850,
    gananciaTotal: 850,
    racha: 5,
    tiempoActivo: "23 días"
  };

  return (
    <>
      <h2 className="text-3xl font-bold mb-6">👤 Mi Perfil</h2>

      {/* Información Personal */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6 mb-6">
        {/* Tarjeta Principal */}
        <div className="lg:col-span-1 bg-gradient-to-br from-blue-800 to-purple-800 rounded-xl p-6 text-center">
          <div className="mb-4">
            <div className="w-24 h-24 bg-gradient-to-br from-orange-400 to-red-500 rounded-full mx-auto flex items-center justify-center text-3xl font-bold">
              {usuario.nombre.charAt(0)}
            </div>
          </div>
          <h3 className="text-xl font-bold mb-2">{usuario.nombre}</h3>
          <p className="text-gray-300 mb-2">{usuario.email}</p>
          <div className="inline-flex items-center bg-yellow-500 text-black px-3 py-1 rounded-full text-sm font-semibold">
            ⭐ {usuario.plan}
          </div>
          <div className="mt-4 space-y-2 text-sm">
            <p>📅 Miembro desde: {usuario.fechaRegistro}</p>
            <p>🕐 Último acceso: {usuario.ultimoAcceso}</p>
            <p>⏱️ Tiempo activo: {usuario.tiempoActivo}</p>
            <p className="text-green-400">✅ Activo hasta: {usuario.fechaExpiracion}</p>
            <p className={`font-semibold ${usuario.estado === 'activo' ? 'text-green-400' : 'text-red-400'}`}>
              🔘 Estado: {usuario.estado.toUpperCase()}
            </p>
          </div>
        </div>

        {/* Estadísticas de Trading */}
        <div className="lg:col-span-2 bg-blue-800 rounded-xl p-6">
          <h3 className="text-xl font-semibold mb-4">📊 Estadísticas de Trading</h3>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            <div className="bg-blue-900 rounded-lg p-4 text-center">
              <div className="text-2xl font-bold text-green-400">{usuario.operacionesTotales}</div>
              <div className="text-sm text-gray-300">Operaciones</div>
            </div>
            <div className="bg-blue-900 rounded-lg p-4 text-center">
              <div className="text-2xl font-bold text-green-400">{usuario.operacionesGanadas}</div>
              <div className="text-sm text-gray-300">Ganadas</div>
            </div>
            <div className="bg-blue-900 rounded-lg p-4 text-center">
              <div className="text-2xl font-bold text-red-400">{usuario.operacionesPerdidas}</div>
              <div className="text-sm text-gray-300">Perdidas</div>
            </div>
            <div className="bg-blue-900 rounded-lg p-4 text-center">
              <div className="text-2xl font-bold text-yellow-400">{usuario.winRate}%</div>
              <div className="text-sm text-gray-300">Win Rate</div>
            </div>
          </div>
        </div>
      </div>

      {/* Rendimiento Financiero */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-6">
        <div className="bg-blue-800 rounded-xl p-6">
          <h3 className="text-xl font-semibold mb-4">💰 Rendimiento Financiero</h3>
          <div className="space-y-4">
            <div className="flex justify-between items-center">
              <span>Balance Inicial:</span>
              <span className="font-semibold">${usuario.balanceInicial}</span>
            </div>
            <div className="flex justify-between items-center">
              <span>Balance Actual:</span>
              <span className="font-semibold text-green-400">${usuario.balanceActual}</span>
            </div>
            <div className="flex justify-between items-center">
              <span>Ganancia Total:</span>
              <span className="font-semibold text-green-400">+${usuario.gananciaTotal}</span>
            </div>
            <div className="flex justify-between items-center">
              <span>Pips Ganados:</span>
              <span className="font-semibold text-blue-300">+{usuario.pipsGanados} pips</span>
            </div>
            <div className="flex justify-between items-center">
              <span>Racha Actual:</span>
              <span className="font-semibold text-yellow-400">{usuario.racha} operaciones</span>
            </div>
          </div>
        </div>

        {/* Configuración de Cuenta */}
        <div className="bg-blue-800 rounded-xl p-6">
          <h3 className="text-xl font-semibold mb-4">⚙️ Configuración</h3>
          <div className="space-y-4">
            <button className="w-full bg-orange-500 hover:bg-orange-600 text-white py-3 rounded-lg font-semibold transition-colors">
              ✏️ Editar Perfil
            </button>
            <button className="w-full bg-blue-600 hover:bg-blue-700 text-white py-3 rounded-lg font-semibold transition-colors">
              🔒 Cambiar Contraseña
            </button>
            <button className="w-full bg-green-600 hover:bg-green-700 text-white py-3 rounded-lg font-semibold transition-colors">
              💳 Gestionar Suscripción
            </button>
            <button className="w-full bg-purple-600 hover:bg-purple-700 text-white py-3 rounded-lg font-semibold transition-colors">
              📱 Configurar Notificaciones
            </button>
            <button
              onClick={cerrarSesion}
              className="w-full bg-red-600 hover:bg-red-700 text-white py-3 rounded-lg font-semibold transition-colors"
            >
              🚪 Cerrar Sesión
            </button>
          </div>
        </div>
      </div>

      {/* Actividad Reciente */}
      <div className="bg-blue-800 rounded-xl p-6">
        <h3 className="text-xl font-semibold mb-4">📈 Actividad Reciente</h3>
        <div className="space-y-3">
          <div className="bg-blue-900 rounded-lg p-4 flex justify-between items-center">
            <div>
              <p className="font-semibold text-green-400">✅ XAUUSD - Operación Cerrada</p>
              <p className="text-sm text-gray-300">+45 pips | +$125</p>
            </div>
            <span className="text-xs text-gray-400">Hace 2 horas</span>
          </div>
          <div className="bg-blue-900 rounded-lg p-4 flex justify-between items-center">
            <div>
              <p className="font-semibold text-blue-400">📊 Análisis Completado</p>
              <p className="text-sm text-gray-300">EURUSD - 5 temporalidades</p>
            </div>
            <span className="text-xs text-gray-400">Hace 4 horas</span>
          </div>
          <div className="bg-blue-900 rounded-lg p-4 flex justify-between items-center">
            <div>
              <p className="font-semibold text-yellow-400">🔔 Nueva Señal Recibida</p>
              <p className="text-sm text-gray-300">GBPUSD - Liquidez Superior</p>
            </div>
            <span className="text-xs text-gray-400">Hace 6 horas</span>
          </div>
        </div>
      </div>
    </>
  );
}

// Componente de la sección de inicio
function SeccionInicio({ setSeccionActiva }) {
  return (
    <>
      <h2 className="text-3xl font-bold mb-4">Bienvenido a GERMAYORI</h2>
      <p className="mb-6 text-sm">Selecciona un canal para comenzar</p>

      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6">
        <Card title="Chat Educativo" desc="Asistente IA Germayori" icon="💬" onClick={() => setSeccionActiva('chat')} />
        <Card title="Trading en Vivo" desc="Operaciones reales jhon0608" icon="📉" tag="LIVE" onClick={() => setSeccionActiva('trading')} />
        <Card title="Calculadora" desc="Pips, profit y riesgo" icon="🧮" onClick={() => setSeccionActiva('calculadora')} />
        <Card title="Noticias" desc="Calendario económico" icon="📰" onClick={() => setSeccionActiva('noticias')} />
        <Card title="Señales" desc="Estrategia Liquidez Germayori" icon="📡" onClick={() => setSeccionActiva('senales')} />
        <Card title="Notificaciones" desc="Sistema de alertas" icon="🔔" onClick={() => setSeccionActiva('notificaciones')} />
        <Card title="Alertas" desc="Mercado en tiempo real" icon="⚠️" onClick={() => setSeccionActiva('alertas')} />
        <Card title="TradingView" desc="Gráficos profesionales en tiempo real" icon="📊" tag="PROFESIONAL" onClick={() => setSeccionActiva('tradingview')} />
        <Card title="Videos Educativos" desc="Cursos GERMAYORI FVG" icon="📘" tag="PREMIUM" onClick={() => setSeccionActiva('videos')} />
        <Card title="Mentoría Directa" desc="Con el creador de GERMAYORI" icon="🎓" tag="VIP EXCLUSIVO" onClick={() => setSeccionActiva('mentoria')} />
      </div>
    </>
  );
}

// Componente de la sección de señales integrada
function SeccionSenales() {
  const [imagenes, setImagenes] = useState({
    diario: null,
    h4: null,
    h1: null,
    min30: null,
    min5: null
  });
  const [resultado, setResultado] = useState("");
  const [cargando, setCargando] = useState(false);

  const manejarCambioImagen = (timeframe, file) => {
    setImagenes(prev => ({
      ...prev,
      [timeframe]: file
    }));
  };

  const enviarImagenes = async (e) => {
    e.preventDefault();

    // Verificar que al menos una imagen esté seleccionada
    const imagenesSeleccionadas = Object.values(imagenes).filter(img => img !== null);
    if (imagenesSeleccionadas.length === 0) {
      setResultado("❌ Debes seleccionar al menos una imagen.");
      return;
    }

    const formData = new FormData();
    Object.entries(imagenes).forEach(([timeframe, file]) => {
      if (file) {
        formData.append(timeframe, file);
      }
    });

    setCargando(true);
    setResultado("⏳ Analizando imágenes con la estrategia Germayori...");

    try {
      const res = await fetch("/api/analizar-imagen", {
        method: "POST",
        body: formData,
      });
      const data = await res.json();
      setResultado(data.resultado || "❌ No se pudo generar la señal.");
    } catch (err) {
      setResultado("❌ Error al procesar las imágenes.");
    } finally {
      setCargando(false);
    }
  };

  const timeframes = [
    { key: 'diario', label: '📅 Diario', desc: 'Gráfico diario' },
    { key: 'h4', label: '🕐 H4', desc: '4 horas' },
    { key: 'h1', label: '🕐 H1', desc: '1 hora' },
    { key: 'min30', label: '⏰ 30min', desc: '30 minutos' },
    { key: 'min5', label: '⚡ 5min', desc: '5 minutos' }
  ];

  return (
    <>
      <div className="flex items-center mb-6">
        <h2 className="text-3xl font-bold">📡 Canal de Señales Germayori</h2>
      </div>

      <div className="bg-blue-800 rounded-xl p-6 shadow-lg">
        <h3 className="text-xl font-semibold mb-4">📤 Análisis Multi-Temporalidad</h3>
        <p className="text-sm text-gray-300 mb-6">
          Sube las imágenes de diferentes temporalidades para un análisis completo con la estrategia de liquidez Germayori
        </p>

        <form onSubmit={enviarImagenes} className="space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {timeframes.map(({ key, label, desc }) => (
              <div key={key} className="bg-blue-900 rounded-lg p-4">
                <label className="block text-sm font-medium mb-2">{label}</label>
                <p className="text-xs text-gray-400 mb-2">{desc}</p>
                <input
                  type="file"
                  accept="image/*"
                  onChange={(e) => manejarCambioImagen(key, e.target.files[0])}
                  className="bg-white text-black p-2 rounded w-full text-sm"
                />
                {imagenes[key] && (
                  <p className="text-xs text-green-400 mt-1">✅ {imagenes[key].name}</p>
                )}
              </div>
            ))}
          </div>

          <div className="flex justify-center">
            <button
              type="submit"
              className="bg-orange-500 hover:bg-orange-600 text-white px-8 py-4 rounded-lg font-semibold transition-colors disabled:opacity-50 text-lg"
              disabled={cargando}
            >
              🚀 {cargando ? "Analizando con Germayori..." : "Analizar con Estrategia Germayori"}
            </button>
          </div>
        </form>

        <div className="mt-8">
          <h4 className="text-lg font-semibold mb-3">📊 Análisis de Liquidez Germayori:</h4>
          <div
            className="bg-blue-900 rounded-lg p-6 text-base whitespace-pre-wrap border-l-4 border-orange-400"
            style={{ minHeight: "150px" }}
          >
            {resultado || "🔍 Sube las imágenes de las temporalidades para obtener el análisis institucional de Germayori..."}
          </div>
        </div>
      </div>
    </>
  );
}

// Componente Chat Educativo
function SeccionChat() {
  const [mensajes, setMensajes] = useState([
    {
      texto: "¡Hola! Soy Germayori 🚀 Estoy aquí para enseñarte mi estrategia de liquidez institucional. ¿Qué quieres aprender hoy?",
      autor: "germayori"
    }
  ]);
  const [input, setInput] = useState("");

  const obtenerRespuestaGermayori = (pregunta) => {
    const preguntaLower = pregunta.toLowerCase();

    if (preguntaLower.includes('liquidez') || preguntaLower.includes('liquidity')) {
      return "🔻🔺 La liquidez es clave en mi estrategia:\n\n🔻 Liquidez Inferior: Cuando el precio liquida un bajo importante, buscamos COMPRA\n🔺 Liquidez Superior: Cuando el precio liquida un alto importante, buscamos VENTA\n\nRecuerda: Los grandes bancos necesitan liquidez para sus órdenes masivas. Nosotros seguimos su rastro.";
    }

    if (preguntaLower.includes('fvg') || preguntaLower.includes('fair value gap') || preguntaLower.includes('gap')) {
      return "📉 Fair Value Gap (FVG) es fundamental:\n\nDespués de liquidar la liquidez, NO entramos inmediatamente. Esperamos un retroceso claro hacia un FVG para una entrada más precisa.\n\nEl FVG es donde el precio 'debe' regresar según la lógica institucional. Es nuestro punto de entrada ideal.";
    }

    if (preguntaLower.includes('order block') || preguntaLower.includes('bloque')) {
      return "🚫 EVITA Order Blocks tradicionales:\n\nLos bloques de órdenes simples pueden fallar y no son confiables por sí solos. En mi estrategia nos enfocamos en:\n\n✅ Desequilibrios\n✅ Expansiones y retrocesos\n✅ Impulsos alineados con liquidez tomada\n\nEsto es más preciso que los OB tradicionales.";
    }

    if (preguntaLower.includes('institucional') || preguntaLower.includes('banco') || preguntaLower.includes('manipulacion')) {
      return "🏦 Estrategia Institucional:\n\nAnalizamos la acción del precio con lógica institucional (flujo de órdenes bancarias):\n\n📊 Desequilibrios - Donde hay más compradores o vendedores\n📈 Expansiones y retrocesos - Movimientos institucionales\n🎯 Impulsos alineados - Confirmación de dirección\n\nLos bancos mueven el mercado, nosotros los seguimos.";
    }

    if (preguntaLower.includes('temporalidad') || preguntaLower.includes('timeframe')) {
      return "⏰ Análisis Multi-Temporalidad:\n\nUsamos 5 temporalidades para confirmación:\n📅 Diario - Tendencia principal\n🕐 H4 - Estructura intermedia\n🕐 H1 - Confirmación\n⏰ 30min - Entrada precisa\n⚡ 5min - Timing exacto\n\nCada temporalidad debe alinearse para una señal fuerte.";
    }

    if (preguntaLower.includes('entrada') || preguntaLower.includes('entry')) {
      return "🎯 Proceso de Entrada:\n\n1️⃣ Identificar liquidez tomada (alto/bajo importante)\n2️⃣ Esperar retroceso hacia FVG\n3️⃣ Confirmar con análisis institucional\n4️⃣ Verificar alineación en múltiples temporalidades\n5️⃣ Ejecutar entrada con gestión de riesgo\n\n¡Paciencia es clave! No todas las liquidaciones son válidas.";
    }

    if (preguntaLower.includes('riesgo') || preguntaLower.includes('risk') || preguntaLower.includes('stop')) {
      return "⛔ Gestión de Riesgo Germayori:\n\n💡 Regla del 2% - Nunca arriesgues más del 2% por operación\n🎯 Risk/Reward 1:3 mínimo\n📍 Stop Loss debajo/encima de la liquidez tomada\n🎯 Take Profit en próximos niveles de liquidez\n\n¡El riesgo controlado es lo que separa a los profesionales!";
    }

    // Respuesta por defecto
    return "🤔 Interesante pregunta. Mi estrategia se basa en:\n\n🔻🔺 Liquidez institucional\n📉 Fair Value Gaps (FVG)\n🏦 Análisis de flujo bancario\n⏰ Múltiples temporalidades\n🚫 Sin Order Blocks tradicionales\n\n¿Sobre cuál de estos temas quieres que profundice?";
  };

  const enviarMensaje = () => {
    if (input.trim() === "") return;

    const nuevoMensaje = { texto: input, autor: "tú" };
    setMensajes([...mensajes, nuevoMensaje]);
    const pregunta = input;
    setInput("");

    setTimeout(() => {
      const respuesta = {
        texto: obtenerRespuestaGermayori(pregunta),
        autor: "germayori",
      };
      setMensajes((mensajesActuales) => [...mensajesActuales, respuesta]);
    }, 1500);
  };

  return (
    <>
      <h2 className="text-3xl font-bold mb-4">💬 Chat Educativo Germayori</h2>
      <div className="bg-blue-800 rounded-xl p-6 shadow-lg">
        <div className="h-96 overflow-y-auto space-y-3 mb-4 bg-gray-900 p-4 rounded-lg border border-blue-600">
          {mensajes.map((msg, index) => (
            <div
              key={index}
              className={`p-4 rounded-lg max-w-4xl ${
                msg.autor === "germayori"
                  ? "bg-gradient-to-r from-indigo-600 to-purple-600 text-white text-left mr-8"
                  : "bg-gradient-to-r from-green-600 to-emerald-600 text-white text-right ml-8"
              }`}
            >
              <div className="font-semibold text-sm mb-1">
                {msg.autor === "germayori" ? "🚀 Germayori" : "👤 Tú"}
              </div>
              <div className="text-white whitespace-pre-line leading-relaxed">
                {msg.texto}
              </div>
            </div>
          ))}
        </div>
        <div className="flex gap-3">
          <input
            value={input}
            onChange={(e) => setInput(e.target.value)}
            placeholder="Pregúntame sobre liquidez, FVG, estrategia institucional..."
            className="flex-1 p-4 rounded-lg text-black font-medium border-2 border-blue-600 focus:border-orange-500 focus:outline-none"
            onKeyPress={(e) => e.key === 'Enter' && enviarMensaje()}
          />
          <button
            onClick={enviarMensaje}
            className="bg-gradient-to-r from-orange-500 to-red-500 hover:from-orange-600 hover:to-red-600 text-white px-8 py-4 rounded-lg font-bold transition-all transform hover:scale-105"
          >
            📤 Enviar
          </button>
        </div>
        <div className="mt-4 text-center">
          <p className="text-sm text-gray-300">
            💡 Pregunta sobre: <span className="text-orange-400">liquidez, FVG, estrategia institucional, temporalidades, gestión de riesgo</span>
          </p>
        </div>
      </div>
    </>
  );
}

// Componente Trading en Vivo
function SeccionTrading() {
  return (
    <>
      <h2 className="text-3xl font-bold mb-4">📉 Trading en Vivo</h2>
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <div className="bg-blue-800 rounded-xl p-6">
          <h3 className="text-xl font-semibold mb-4 flex items-center">
            🔴 LIVE - Operaciones Reales jhon0608
          </h3>
          <div className="bg-blue-900 rounded p-4 mb-4">
            <p className="text-green-400 font-semibold">✅ XAUUSD - COMPRA ACTIVA</p>
            <p className="text-sm">Entrada: 2,325.50 | SL: 2,310.00 | TP: 2,355.00</p>
            <p className="text-xs text-gray-400">Hace 15 minutos</p>
          </div>
          <div className="bg-blue-900 rounded p-4">
            <p className="text-red-400 font-semibold">❌ EURUSD - CERRADA</p>
            <p className="text-sm">Resultado: +45 pips</p>
            <p className="text-xs text-gray-400">Hace 2 horas</p>
          </div>
        </div>
        <div className="bg-blue-800 rounded-xl p-6">
          <h3 className="text-xl font-semibold mb-4">📊 Estadísticas del Día</h3>
          <div className="space-y-3">
            <div className="flex justify-between">
              <span>Operaciones:</span>
              <span className="font-semibold">3</span>
            </div>
            <div className="flex justify-between">
              <span>Ganadas:</span>
              <span className="text-green-400 font-semibold">2</span>
            </div>
            <div className="flex justify-between">
              <span>Perdidas:</span>
              <span className="text-red-400 font-semibold">1</span>
            </div>
            <div className="flex justify-between">
              <span>Pips totales:</span>
              <span className="text-green-400 font-semibold">+78</span>
            </div>
          </div>
        </div>
      </div>
    </>
  );
}

// Componente Calculadora
function SeccionCalculadora() {
  const [pips, setPips] = useState('');
  const [lotSize, setLotSize] = useState('');
  const [resultado, setResultado] = useState(null);

  const calcular = () => {
    if (pips && lotSize) {
      const profit = parseFloat(pips) * parseFloat(lotSize) * 10;
      setResultado(profit);
    }
  };

  return (
    <>
      <h2 className="text-3xl font-bold mb-4">🧮 Calculadora de Trading</h2>
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <div className="bg-blue-800 rounded-xl p-6">
          <h3 className="text-xl font-semibold mb-4">💰 Calculadora de Pips</h3>
          <div className="space-y-4">
            <div>
              <label className="block text-sm font-medium mb-2 text-white">Pips:</label>
              <input
                type="number"
                value={pips}
                onChange={(e) => setPips(e.target.value)}
                className="w-full p-3 rounded text-black font-semibold border-2 border-blue-600 focus:border-orange-500 focus:outline-none"
                placeholder="Ej: 50"
              />
            </div>
            <div>
              <label className="block text-sm font-medium mb-2 text-white">Tamaño del Lote:</label>
              <input
                type="number"
                step="0.01"
                value={lotSize}
                onChange={(e) => setLotSize(e.target.value)}
                className="w-full p-3 rounded text-black font-semibold border-2 border-blue-600 focus:border-orange-500 focus:outline-none"
                placeholder="Ej: 0.1"
              />
            </div>
            <button
              onClick={calcular}
              className="w-full bg-orange-500 hover:bg-orange-600 text-white py-3 rounded font-semibold"
            >
              Calcular Profit
            </button>
            {resultado !== null && (
              <div className="bg-blue-900 rounded p-4 text-center">
                <p className="text-lg font-semibold text-green-400">
                  Profit: ${resultado.toFixed(2)}
                </p>
              </div>
            )}
          </div>
        </div>
        <div className="bg-blue-800 rounded-xl p-6">
          <h3 className="text-xl font-semibold mb-4">📊 Gestión de Riesgo</h3>
          <div className="space-y-3 text-sm">
            <div className="bg-blue-900 rounded p-3">
              <p className="font-semibold">💡 Regla del 2%</p>
              <p>Nunca arriesgues más del 2% de tu cuenta por operación</p>
            </div>
            <div className="bg-blue-900 rounded p-3">
              <p className="font-semibold">🎯 Risk/Reward 1:3</p>
              <p>Por cada $1 de riesgo, busca $3 de ganancia</p>
            </div>
            <div className="bg-blue-900 rounded p-3">
              <p className="font-semibold">📈 Diversificación</p>
              <p>No pongas todos los huevos en la misma canasta</p>
            </div>
          </div>
        </div>
      </div>
    </>
  );
}

// Componente Noticias
function SeccionNoticias() {
  const noticias = [
    { titulo: "📈 Fed mantiene tasas de interés", tiempo: "Hace 2 horas", impacto: "Alto" },
    { titulo: "💰 Datos de empleo mejor de lo esperado", tiempo: "Hace 4 horas", impacto: "Medio" },
    { titulo: "🏦 BCE considera cambios en política monetaria", tiempo: "Hace 6 horas", impacto: "Alto" },
    { titulo: "📊 PIB trimestral supera expectativas", tiempo: "Hace 8 horas", impacto: "Medio" }
  ];

  return (
    <>
      <h2 className="text-3xl font-bold mb-4">📰 Noticias y Calendario Económico</h2>

      {/* Calendarios Económicos */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-6">
        {/* Investing.com Calendar */}
        <div className="bg-blue-800 rounded-xl p-6">
          <h3 className="text-xl font-semibold mb-4">📊 Investing.com - Calendario Económico</h3>
          <div
            className="bg-gradient-to-br from-blue-900 to-purple-900 rounded-lg p-8 text-center cursor-pointer hover:scale-105 transition-transform"
            onClick={() => {
              console.log('Cubículo clickeado - Abriendo calendario económico');
              window.open('https://es.investing.com/economic-calendar/', '_blank');
            }}
          >
            <div className="text-8xl mb-6">📈</div>
            <div className="space-y-4">
              <div className="bg-blue-800 rounded-lg p-4">
                <div className="flex justify-between items-center">
                  <span className="text-sm">🇺🇸 NFP</span>
                  <span className="bg-red-500 text-xs px-2 py-1 rounded">ALTO</span>
                </div>
                <div className="text-lg font-bold mt-2">14:30 GMT</div>
              </div>
              <div className="bg-blue-800 rounded-lg p-4">
                <div className="flex justify-between items-center">
                  <span className="text-sm">🇪🇺 CPI</span>
                  <span className="bg-yellow-500 text-black text-xs px-2 py-1 rounded">MEDIO</span>
                </div>
                <div className="text-lg font-bold mt-2">16:00 GMT</div>
              </div>
              <div className="bg-blue-800 rounded-lg p-4">
                <div className="flex justify-between items-center">
                  <span className="text-sm">🇬🇧 BOE</span>
                  <span className="bg-red-500 text-xs px-2 py-1 rounded">ALTO</span>
                </div>
                <div className="text-lg font-bold mt-2">18:00 GMT</div>
              </div>
            </div>
            <div className="mt-6 text-blue-300 text-sm">
              🔗 https://es.investing.com/economic-calendar/
            </div>
          </div>
          <div className="mt-4 text-center">
            <button
              onClick={() => {
                console.log('Botón clickeado - Abriendo calendario económico');
                window.open('https://es.investing.com/economic-calendar/', '_blank');
              }}
              className="bg-gradient-to-r from-blue-500 to-purple-600 hover:from-blue-600 hover:to-purple-700 text-white px-6 py-3 rounded-lg font-semibold transition-all transform hover:scale-105"
            >
              🚀 Abrir Investing.com
            </button>
          </div>
        </div>

        {/* MyFxBook Calendar */}
        <div className="bg-blue-800 rounded-xl p-6">
          <h3 className="text-xl font-semibold mb-4">📅 MyFxBook - Calendario en Tiempo Real</h3>
          <div className="bg-gradient-to-br from-orange-900 to-red-900 rounded-lg p-8 text-center">
            <div className="text-8xl mb-6">📊</div>
            <div className="space-y-4">
              <div className="bg-orange-800 rounded-lg p-4">
                <div className="flex justify-between items-center">
                  <span className="text-sm">💰 FOMC</span>
                  <span className="bg-red-500 text-xs px-2 py-1 rounded">MUY ALTO</span>
                </div>
                <div className="text-lg font-bold mt-2">20:00 GMT</div>
              </div>
              <div className="bg-orange-800 rounded-lg p-4">
                <div className="flex justify-between items-center">
                  <span className="text-sm">🏦 ECB</span>
                  <span className="bg-red-500 text-xs px-2 py-1 rounded">ALTO</span>
                </div>
                <div className="text-lg font-bold mt-2">12:45 GMT</div>
              </div>
              <div className="bg-orange-800 rounded-lg p-4">
                <div className="flex justify-between items-center">
                  <span className="text-sm">📈 GDP</span>
                  <span className="bg-yellow-500 text-black text-xs px-2 py-1 rounded">MEDIO</span>
                </div>
                <div className="text-lg font-bold mt-2">15:30 GMT</div>
              </div>
            </div>
          </div>
          <div className="mt-4 text-center">
            <button
              onClick={() => window.open('https://www.myfxbook.com/forex-economic-calendar', '_blank')}
              className="bg-gradient-to-r from-orange-500 to-red-500 hover:from-orange-600 hover:to-red-600 text-white px-6 py-3 rounded-lg font-semibold transition-all transform hover:scale-105"
            >
              📊 Abrir MyFxBook
            </button>
          </div>
        </div>
      </div>

      {/* Noticias Destacadas */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <div className="bg-blue-800 rounded-xl p-6">
          <h3 className="text-xl font-semibold mb-4">🔥 Noticias Destacadas</h3>
          <div className="space-y-4">
            {noticias.map((noticia, index) => (
              <div key={index} className="bg-blue-900 rounded p-4 border-l-4 border-orange-500">
                <div className="flex justify-between items-start mb-2">
                  <h4 className="font-semibold">{noticia.titulo}</h4>
                  <span className={`text-xs px-2 py-1 rounded ${
                    noticia.impacto === 'Alto' ? 'bg-red-500' : 'bg-yellow-500'
                  }`}>
                    {noticia.impacto}
                  </span>
                </div>
                <p className="text-sm text-gray-400">{noticia.tiempo}</p>
              </div>
            ))}
          </div>
        </div>

        {/* Panel de Análisis */}
        <div className="bg-blue-800 rounded-xl p-6">
          <h3 className="text-xl font-semibold mb-4">📊 Análisis de Impacto</h3>
          <div className="space-y-4">
            <div className="bg-red-900 border border-red-500 rounded p-4">
              <h4 className="font-semibold text-red-300 mb-2">🔴 Alto Impacto</h4>
              <p className="text-sm text-gray-300">Eventos que pueden mover significativamente el mercado</p>
            </div>
            <div className="bg-yellow-900 border border-yellow-500 rounded p-4">
              <h4 className="font-semibold text-yellow-300 mb-2">🟡 Medio Impacto</h4>
              <p className="text-sm text-gray-300">Eventos con impacto moderado en la volatilidad</p>
            </div>
            <div className="bg-green-900 border border-green-500 rounded p-4">
              <h4 className="font-semibold text-green-300 mb-2">🟢 Bajo Impacto</h4>
              <p className="text-sm text-gray-300">Eventos informativos con menor impacto</p>
            </div>
          </div>
        </div>
      </div>
    </>
  );
}

// Componente Notificaciones
function SeccionNotificaciones() {
  const [notificaciones] = useState([
    { id: 1, tipo: 'señal', mensaje: '🟢 Nueva señal XAUUSD disponible', tiempo: 'Hace 5 min' },
    { id: 2, tipo: 'alerta', mensaje: '⚠️ Nivel de liquidez alcanzado en EURUSD', tiempo: 'Hace 15 min' },
    { id: 3, tipo: 'educativo', mensaje: '📚 Nuevo video sobre FVG disponible', tiempo: 'Hace 1 hora' }
  ]);

  return (
    <>
      <h2 className="text-3xl font-bold mb-4">🔔 Notificaciones</h2>
      <div className="bg-blue-800 rounded-xl p-6">
        <h3 className="text-xl font-semibold mb-4">📬 Centro de Notificaciones</h3>
        <div className="space-y-3">
          {notificaciones.map((notif) => (
            <div key={notif.id} className="bg-blue-900 rounded p-4 flex justify-between items-center">
              <div>
                <p className="font-semibold">{notif.mensaje}</p>
                <p className="text-sm text-gray-400">{notif.tiempo}</p>
              </div>
              <button className="text-gray-400 hover:text-white">✕</button>
            </div>
          ))}
        </div>
      </div>
    </>
  );
}

// Componente Alertas
function SeccionAlertas() {
  return (
    <>
      <h2 className="text-3xl font-bold mb-4">⚠️ Alertas de Mercado</h2>
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <div className="bg-blue-800 rounded-xl p-6">
          <h3 className="text-xl font-semibold mb-4">🚨 Alertas Activas</h3>
          <div className="space-y-3">
            <div className="bg-red-900 border border-red-500 rounded p-3">
              <p className="font-semibold text-red-300">🔴 XAUUSD - Liquidez Superior</p>
              <p className="text-sm">Precio: 2,340.50 | Objetivo: 2,345.00</p>
            </div>
            <div className="bg-yellow-900 border border-yellow-500 rounded p-3">
              <p className="font-semibold text-yellow-300">🟡 EURUSD - FVG Detectado</p>
              <p className="text-sm">Precio: 1.0850 | Zona: 1.0845-1.0855</p>
            </div>
          </div>
        </div>
        <div className="bg-blue-800 rounded-xl p-6">
          <h3 className="text-xl font-semibold mb-4">⚙️ Configurar Alertas</h3>
          <div className="space-y-4">
            <div>
              <label className="block text-sm font-medium mb-2 text-white">Par de divisas:</label>
              <select className="w-full p-3 rounded text-black font-semibold border-2 border-blue-600 focus:border-orange-500 focus:outline-none">
                <option>XAUUSD</option>
                <option>EURUSD</option>
                <option>GBPUSD</option>
              </select>
            </div>
            <div>
              <label className="block text-sm font-medium mb-2 text-white">Precio objetivo:</label>
              <input
                type="number"
                className="w-full p-3 rounded text-black font-semibold border-2 border-blue-600 focus:border-orange-500 focus:outline-none"
                placeholder="2340.50"
              />
            </div>
            <button className="w-full bg-orange-500 hover:bg-orange-600 text-white py-3 rounded font-semibold">
              Crear Alerta
            </button>
          </div>
        </div>
      </div>
    </>
  );
}

// Componente TradingView
function SeccionTradingView() {
  const [parSeleccionado, setParSeleccionado] = useState('XAUUSD');

  // Función para obtener el símbolo correcto para TradingView
  const obtenerSimboloTradingView = (symbol) => {
    const simbolos = {
      'XAUUSD': 'FX_IDC:XAUUSD',
      'EURUSD': 'FX_IDC:EURUSD',
      'GBPUSD': 'FX_IDC:GBPUSD',
      'USDJPY': 'FX_IDC:USDJPY',
      'GBPJPY': 'FX_IDC:GBPJPY',
      'EURJPY': 'FX_IDC:EURJPY',
      'AUDUSD': 'FX_IDC:AUDUSD',
      'USDCAD': 'FX_IDC:USDCAD',
      'US30': 'FOREXCOM:DJI',
      'SPX500': 'FOREXCOM:SPXUSD',
      'NAS100': 'FOREXCOM:NSXUSD'
    };
    return simbolos[symbol] || `FX_IDC:${symbol}`;
  };

  const pares = [
    { symbol: 'XAUUSD', name: 'Oro/USD', category: 'Metales' },
    { symbol: 'EURUSD', name: 'Euro/USD', category: 'Forex' },
    { symbol: 'GBPUSD', name: 'Libra/USD', category: 'Forex' },
    { symbol: 'USDJPY', name: 'USD/Yen', category: 'Forex' },
    { symbol: 'GBPJPY', name: 'Libra/Yen', category: 'Forex' },
    { symbol: 'EURJPY', name: 'Euro/Yen', category: 'Forex' },
    { symbol: 'AUDUSD', name: 'AUD/USD', category: 'Forex' },
    { symbol: 'USDCAD', name: 'USD/CAD', category: 'Forex' },
    { symbol: 'US30', name: 'Dow Jones', category: 'Índices' },
    { symbol: 'SPX500', name: 'S&P 500', category: 'Índices' },
    { symbol: 'NAS100', name: 'Nasdaq', category: 'Índices' }
  ];

  return (
    <>
      <h2 className="text-3xl font-bold mb-4">📉 TradingView - Análisis en Tiempo Real</h2>

      {/* Selector de pares por categorías */}
      <div className="mb-6">
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          {/* Forex */}
          <div className="bg-blue-800 rounded-xl p-4">
            <h4 className="text-lg font-semibold mb-3 text-center">💱 Forex</h4>
            <div className="flex flex-wrap gap-2 justify-center">
              {pares.filter(par => par.category === 'Forex').map((par) => (
                <button
                  key={par.symbol}
                  onClick={() => setParSeleccionado(par.symbol)}
                  className={`px-3 py-2 rounded-lg font-semibold transition-all text-sm ${
                    parSeleccionado === par.symbol
                      ? 'bg-orange-500 text-white'
                      : 'bg-blue-900 text-white hover:bg-blue-700'
                  }`}
                >
                  {par.symbol}
                </button>
              ))}
            </div>
          </div>

          {/* Índices */}
          <div className="bg-blue-800 rounded-xl p-4">
            <h4 className="text-lg font-semibold mb-3 text-center">📈 Índices</h4>
            <div className="flex flex-wrap gap-2 justify-center">
              {pares.filter(par => par.category === 'Índices').map((par) => (
                <button
                  key={par.symbol}
                  onClick={() => setParSeleccionado(par.symbol)}
                  className={`px-3 py-2 rounded-lg font-semibold transition-all text-sm ${
                    parSeleccionado === par.symbol
                      ? 'bg-orange-500 text-white'
                      : 'bg-blue-900 text-white hover:bg-blue-700'
                  }`}
                >
                  {par.symbol}
                </button>
              ))}
            </div>
          </div>

          {/* Metales */}
          <div className="bg-blue-800 rounded-xl p-4">
            <h4 className="text-lg font-semibold mb-3 text-center">🥇 Metales</h4>
            <div className="flex flex-wrap gap-2 justify-center">
              {pares.filter(par => par.category === 'Metales').map((par) => (
                <button
                  key={par.symbol}
                  onClick={() => setParSeleccionado(par.symbol)}
                  className={`px-3 py-2 rounded-lg font-semibold transition-all text-sm ${
                    parSeleccionado === par.symbol
                      ? 'bg-orange-500 text-white'
                      : 'bg-blue-900 text-white hover:bg-blue-700'
                  }`}
                >
                  {par.symbol}
                </button>
              ))}
            </div>
          </div>
        </div>
      </div>

      {/* Gráfico Principal */}
      <div className="bg-blue-800 rounded-xl p-4 mb-6">
        <h3 className="text-xl font-semibold mb-4">📊 Gráfico {parSeleccionado}</h3>
        <div className="bg-white rounded-lg overflow-hidden">
          <iframe
            src={`https://s.tradingview.com/widgetembed/?frameElementId=tradingview_chart&symbol=${obtenerSimboloTradingView(parSeleccionado)}&interval=1H&hidesidetoolbar=1&hidetoptoolbar=1&symboledit=1&saveimage=1&toolbarbg=F1F3F6&studies=[]&hideideas=1&theme=Light&style=1&timezone=Etc%2FUTC&studies_overrides={}&overrides={}&enabled_features=[]&disabled_features=[]&locale=es&utm_source=localhost&utm_medium=widget&utm_campaign=chart&utm_term=${obtenerSimboloTradingView(parSeleccionado)}`}
            width="100%"
            height="500"
            frameBorder="0"
            allowTransparency="true"
            scrolling="no"
            allowFullScreen={true}
          ></iframe>
        </div>
      </div>



      {/* Botones de acción */}
      <div className="mt-6 flex gap-4 justify-center">
        <button
          onClick={() => window.open('https://www.tradingview.com/', '_blank')}
          className="bg-gradient-to-r from-blue-500 to-purple-600 hover:from-blue-600 hover:to-purple-700 text-white px-8 py-3 rounded-lg font-semibold transition-all transform hover:scale-105"
        >
          🚀 Abrir TradingView Completo
        </button>
        <button
          onClick={() => window.open('https://www.tradingview.com/chart/', '_blank')}
          className="bg-gradient-to-r from-green-500 to-teal-600 hover:from-green-600 hover:to-teal-700 text-white px-8 py-3 rounded-lg font-semibold transition-all transform hover:scale-105"
        >
          📊 Crear Gráfico Personalizado
        </button>
      </div>
    </>
  );
}

// Componente Videos
function SeccionVideos() {
  const videos = [
    { titulo: "📚 Introducción a la Liquidez Institucional", duracion: "15:30", premium: true },
    { titulo: "🎯 Identificando Fair Value Gaps", duracion: "22:45", premium: true },
    { titulo: "💡 Estrategia Germayori Completa", duracion: "45:20", premium: true }
  ];

  return (
    <>
      <h2 className="text-3xl font-bold mb-4">📘 Videos Educativos</h2>
      <div className="bg-blue-800 rounded-xl p-6">
        <h3 className="text-xl font-semibold mb-4">🎓 Cursos GERMAYORI FVG</h3>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          {videos.map((video, index) => (
            <div key={index} className="bg-blue-900 rounded p-4">
              <div className="flex justify-between items-start mb-2">
                <h4 className="font-semibold text-sm">{video.titulo}</h4>
                {video.premium && <span className="bg-yellow-500 text-black text-xs px-2 py-1 rounded">PREMIUM</span>}
              </div>
              <p className="text-sm text-gray-400 mb-3">Duración: {video.duracion}</p>
              <button className="w-full bg-orange-500 hover:bg-orange-600 text-white py-2 rounded text-sm font-semibold">
                Ver Video
              </button>
            </div>
          ))}
        </div>
      </div>
    </>
  );
}

// Componente Mentoría
function SeccionMentoria() {
  return (
    <>
      <h2 className="text-3xl font-bold mb-4">🎓 Mentoría Directa</h2>
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <div className="bg-blue-800 rounded-xl p-6">
          <h3 className="text-xl font-semibold mb-4">👨‍🏫 Con el creador de GERMAYORI</h3>
          <div className="space-y-4">
            <div className="bg-blue-900 rounded p-4">
              <h4 className="font-semibold mb-2">🔥 Sesión 1 a 1</h4>
              <p className="text-sm text-gray-300">Mentoría personalizada con análisis de tus operaciones</p>
            </div>
            <div className="bg-blue-900 rounded p-4">
              <h4 className="font-semibold mb-2">📞 Llamada Semanal</h4>
              <p className="text-sm text-gray-300">Revisión semanal de estrategias y mercado</p>
            </div>
            <div className="bg-blue-900 rounded p-4">
              <h4 className="font-semibold mb-2">💬 Chat Directo</h4>
              <p className="text-sm text-gray-300">Acceso directo para consultas urgentes</p>
            </div>
          </div>
        </div>
        <div className="bg-blue-800 rounded-xl p-6">
          <h3 className="text-xl font-semibold mb-4">💎 Acceso VIP EXCLUSIVO</h3>
          <div className="text-center">
            <div className="text-6xl mb-4">👑</div>
            <p className="text-lg font-semibold mb-2">Mentoría Premium</p>
            <p className="text-gray-300 mb-4">Acceso limitado solo para miembros VIP</p>
            <button
              onClick={() => window.open('https://chat.whatsapp.com/L4OdlXIE4Kx3av3TSQVOS6', '_blank')}
              className="bg-pink-500 hover:bg-pink-600 text-white px-8 py-3 rounded font-semibold transition-all transform hover:scale-105"
            >
              📱 Solicitar Acceso VIP
            </button>
          </div>
        </div>
      </div>
    </>
  );
}

function Card({ title, desc, icon, tag, onClick }) {
  return (
    <div
      className="bg-blue-800 rounded-xl p-4 shadow hover:scale-105 transition-transform cursor-pointer"
      onClick={onClick}
    >
      <div className="flex items-center justify-between mb-2">
        <span className="text-2xl">{icon}</span>
        {tag && <span className="bg-pink-500 text-white text-xs px-2 py-0.5 rounded-full">{tag}</span>}
      </div>
      <h3 className="font-bold text-lg">{title}</h3>
      <p className="text-sm text-gray-200">{desc}</p>
    </div>
  );
}
