import { connectToDatabase } from '../../lib/mongodb';

export default async function handler(req, res) {
  try {
    console.log('🔄 Intentando conectar a MongoDB...');
    
    const { db } = await connectToDatabase();
    console.log('✅ Conexión exitosa a MongoDB');
    
    const collection = db.collection('usuarios');
    const count = await collection.countDocuments();
    
    console.log(`📊 Usuarios en la base de datos: ${count}`);
    
    return res.status(200).json({ 
      success: true, 
      message: 'Conexión exitosa a MongoDB',
      usuariosCount: count,
      database: 'legendaria-germayori',
      collection: 'usuarios'
    });
    
  } catch (error) {
    console.error('❌ Error conectando a MongoDB:', error);
    return res.status(500).json({ 
      success: false, 
      message: 'Error de conexión a MongoDB',
      error: error.message,
      stack: error.stack
    });
  }
}
