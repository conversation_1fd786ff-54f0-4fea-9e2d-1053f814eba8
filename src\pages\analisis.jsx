import React from "react";

export default function CanalAnalisis() {
  const analisis = [
    {
      titulo: "XAUUSD - Entrada institucional validada",
      imagen: "/analisis/oro-zona-liquidez.png",
      descripcion: "Estructura M15 con desequilibrio rellenado y entrada tras manipulación en zona de liquidez con alto volumen. TP en FVG diario.",
    },
    {
      titulo: "EURUSD - Swing trade con acumulación",
      imagen: "/analisis/eurusd-smartmoney.png",
      descripcion: "Acumulación clara en H4 y rompimiento de estructura con mitigación de OB. TP1 alcanzado, TP2 en espera.",
    },
  ];

  return (
    <div className="min-h-screen bg-gray-900 text-white p-6">
      <h1 className="text-3xl font-bold mb-6 text-center">📊 Canal de Análisis de Mercado</h1>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-8 max-w-5xl mx-auto">
        {analisis.map((a, index) => (
          <div key={index} className="bg-gray-800 rounded-lg p-4 shadow-md">
            <h2 className="text-xl font-semibold mb-2">{a.titulo}</h2>
            <img
              src={a.imagen}
              alt={a.titulo}
              className="w-full h-64 object-cover rounded mb-2"
            />
            <p className="text-sm text-gray-300">{a.descripcion}</p>
          </div>
        ))}
      </div>
    </div>
  );
}
