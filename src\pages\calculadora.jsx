import React, { useState } from "react";

export default function Calculadora() {
  const [balance, setBalance] = useState("");
  const [riesgo, setRiesgo] = useState("");
  const [stopLoss, setStopLoss] = useState("");
  const [resultado, setResultado] = useState(null);

  const calcularLote = () => {
    const balanceNum = parseFloat(balance);
    const riesgoNum = parseFloat(riesgo);
    const stopLossNum = parseFloat(stopLoss);

    if (isNaN(balanceNum) || isNaN(riesgoNum) || isNaN(stopLossNum)) {
      setResultado("⚠️ Por favor, completa todos los campos correctamente.");
      return;
    }

    const riesgoUSD = (riesgoNum / 100) * balanceNum;
    const lote = riesgoUSD / stopLossNum / 10;

    setResultado(`✅ Tamaño de lote: ${lote.toFixed(2)}`);
  };

  return (
    <div className="min-h-screen bg-gray-900 text-white p-6">
      <h1 className="text-3xl font-bold mb-6 text-center">🧮 Calculadora de Lotes</h1>

      <div className="max-w-xl mx-auto bg-gray-800 p-6 rounded-lg shadow-lg space-y-4">
        <input
          type="number"
          placeholder="💰 Balance en USD"
          value={balance}
          onChange={(e) => setBalance(e.target.value)}
          className="w-full p-2 rounded text-black"
        />
        <input
          type="number"
          placeholder="📉 Riesgo %"
          value={riesgo}
          onChange={(e) => setRiesgo(e.target.value)}
          className="w-full p-2 rounded text-black"
        />
        <input
          type="number"
          placeholder="⛔ Stop Loss (pips)"
          value={stopLoss}
          onChange={(e) => setStopLoss(e.target.value)}
          className="w-full p-2 rounded text-black"
        />

        <button
          onClick={calcularLote}
          className="bg-green-600 hover:bg-green-700 px-4 py-2 rounded w-full"
        >
          Calcular Tamaño de Lote
        </button>

        {resultado && (
          <div className="mt-4 text-lg bg-blue-800 p-4 rounded">{resultado}</div>
        )}
      </div>
    </div>
  );
}
