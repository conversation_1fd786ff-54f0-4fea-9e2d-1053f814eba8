import React from "react";

export default function CanalInversiones() {
  const planes = [
    {
      nombre: "Cuenta gestionada 💰",
      detalle: "Invierte desde $500 con gestión profesional. Retorno promedio mensual del 8% al 15%. Sin compromiso de permanencia.",
    },
    {
      nombre: "Fondeo con FTMO 🔓",
      detalle: "Te preparamos y presentamos ante firmas de fondeo como FTMO o MyForexFunds. Estrategia institucional validada.",
    },
    {
      nombre: "Inversión pasiva 📉➡📈",
      detalle: "Ideal para quienes desean rentabilidad sin operar. Participación en pool de capital gestionado con IA + Trading institucional.",
    },
  ];

  return (
    <div className="min-h-screen bg-gray-900 text-white p-6">
      <h1 className="text-3xl font-bold text-center mb-6">💼 Canal de Inversiones</h1>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-6 max-w-4xl mx-auto">
        {planes.map((plan, index) => (
          <div key={index} className="bg-gray-800 rounded-lg p-4 shadow-md">
            <h2 className="text-xl font-semibold mb-2">{plan.nombre}</h2>
            <p className="text-gray-300">{plan.detalle}</p>
          </div>
        ))}
      </div>
    </div>
  );
}
