import React, { useState } from "react";

export default function CanalSenales() {
  const [imagen, setImagen] = useState(null);
  const [resultado, setResultado] = useState("");
  const [cargando, setCargando] = useState(false);

  const enviarImagen = async (e) => {
    e.preventDefault();
    if (!imagen) return;

    const formData = new FormData();
    formData.append("file", imagen);
    setCargando(true);
    setResultado("⏳ Analizando imagen...");

    try {
      const res = await fetch("/api/analizar-imagen", {
        method: "POST",
        body: formData,
      });
      const data = await res.json();
      setResultado(data.resultado || "❌ No se pudo generar la señal.");
    } catch (err) {
      setResultado("❌ Error al procesar la imagen.");
    } finally {
      setCargando(false);
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-900 to-indigo-900 text-white p-6">
      <h1 className="text-3xl font-bold mb-4">🚀 GERAYORI - Dashboard Principal</h1>
      <h2 className="text-2xl font-semibold mb-6">📢 Canal de Señales Germayori</h2>

      <form onSubmit={enviarImagen} className="space-y-4">
        <input
          type="file"
          accept="image/*"
          onChange={(e) => setImagen(e.target.files[0])}
          className="bg-white text-black p-2 rounded"
          required
        />
        <br />
        <button
          type="submit"
          className="bg-orange-500 hover:bg-orange-600 text-white px-4 py-2 rounded"
          disabled={cargando}
        >
          📤 {cargando ? "Analizando..." : "Enviar Imagen"}
        </button>
      </form>

      <div
        className="mt-6 p-4 bg-blue-800 rounded text-lg whitespace-pre-wrap"
        style={{ minHeight: "100px" }}
      >
        {resultado}
      </div>
    </div>
  );
}
