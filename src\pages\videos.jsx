import React from "react";

export default function VideosEducativos() {
  const videos = [
    {
      titulo: "🔥 Introducción a Smart Money y Liquidez",
      url: "https://www.youtube.com/embed/jWlZ3p-HW6Y",
    },
    {
      titulo: "📊 Gestión de riesgo profesional",
      url: "https://www.youtube.com/embed/2ibL3lkoRYI",
    },
    {
      titulo: "📈 Estrategia institucional completa",
      url: "https://www.youtube.com/embed/Q1VVvTYBYIM",
    },
  ];

  return (
    <div className="min-h-screen bg-gray-900 text-white p-6">
      <h1 className="text-3xl font-bold text-center mb-6">🎓 Videos Educativos Germayori</h1>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-8 max-w-5xl mx-auto">
        {videos.map((video, index) => (
          <div key={index} className="bg-gray-800 rounded-lg p-4 shadow-md">
            <h2 className="text-xl font-semibold mb-2">{video.titulo}</h2>
            <div className="aspect-w-16 aspect-h-9">
              <iframe
                src={video.url}
                title={video.titulo}
                allowFullScreen
                className="w-full h-64 rounded"
              ></iframe>
            </div>
          </div>
        ))}
      </div>
    </div>
  );
}
