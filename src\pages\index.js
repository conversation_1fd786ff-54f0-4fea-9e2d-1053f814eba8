import { useState } from 'react';
import Head from 'next/head';

export default function LandingPage() {
  const [formData, setFormData] = useState({
    nombre: '',
    email: '',
    telefono: '',
    edad: '',
    pais: '',
    password: ''
  });
  const [mostrarPago, setMostrarPago] = useState(false);
  const [datosUsuario, setDatosUsuario] = useState(null);

  const handleInputChange = (e) => {
    setFormData({
      ...formData,
      [e.target.name]: e.target.value
    });
  };

  const handleRegistro = async (e) => {
    e.preventDefault();

    try {
      const response = await fetch('/api/registro', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          nombre: formData.nombre,
          email: formData.email,
          telefono: formData.telefono,
          edad: formData.edad,
          pais: formData.pais,
          password: formData.password
        })
      });

      const data = await response.json();

      if (data.success) {
        setDatosUsuario(data);
        setMostrarPago(true);

        // LIMPIAR EL FORMULARIO para que no se vea el registro anterior
        setFormData({
          nombre: '',
          email: '',
          telefono: '',
          edad: '',
          pais: '',
          password: ''
        });

        // Scroll hacia la sección de pago
        setTimeout(() => {
          document.getElementById('pago-yappy')?.scrollIntoView({ behavior: 'smooth' });
        }, 100);
      } else {
        alert('Error en el registro: ' + data.message);
      }
    } catch (error) {
      console.error('Error:', error);
      alert('Error de conexión. Intenta de nuevo.');
    }
  };

  return (
    <>
      <Head>
        <title>GERMAYORI - Estrategia de Liquidez Institucional</title>
        <meta name="description" content="Aprende la estrategia de trading más avanzada con Germayori. Liquidez institucional, FVG y análisis profesional." />
      </Head>

      <div className="min-h-screen text-white overflow-x-hidden" style={{
        background: `
          linear-gradient(135deg, rgba(30, 58, 138, 0.3), rgba(67, 56, 202, 0.3)),
          radial-gradient(circle at 20% 20%, rgba(255, 165, 0, 0.15) 0%, transparent 50%),
          radial-gradient(circle at 80% 20%, rgba(255, 69, 0, 0.15) 0%, transparent 50%),
          radial-gradient(circle at 50% 80%, rgba(0, 255, 255, 0.08) 0%, transparent 50%),
          linear-gradient(45deg, #0f172a 0%, #1e3a8a 25%, #3730a3 50%, #1e1b4b 75%, #0f172a 100%)
        `,
        filter: 'brightness(1.4) contrast(1.3) saturate(1.5)'
      }}>

        {/* Header */}
        <header className="relative z-10 p-6">
          <nav className="flex justify-between items-center max-w-7xl mx-auto">
            <div className="flex items-center space-x-3">
              <div className="flex items-center space-x-2">
                <img
                  src="/logo.png"
                  alt="GERMAYORI"
                  className="w-12 h-12 rounded-full border-2 border-orange-400"
                />
                <span className="text-2xl font-bold text-white">GERMAYORI</span>
              </div>
            </div>

            <div className="flex items-center space-x-6">
              {/* Enlaces de navegación */}
              <div className="hidden md:flex space-x-6">
                <a href="#estrategia" className="hover:text-orange-400 transition-colors font-semibold">Estrategia</a>
                <a href="#resultados" className="hover:text-orange-400 transition-colors font-semibold">Resultados</a>
                <a href="#registro" className="hover:text-orange-400 transition-colors font-semibold">Registro</a>
              </div>

              {/* BOTÓN DE INICIAR SESIÓN - MUY VISIBLE */}
              <a
                href="/login"
                className="bg-gradient-to-r from-blue-500 to-indigo-600 hover:from-blue-600 hover:to-indigo-700 text-white px-6 py-3 rounded-xl font-bold text-lg transition-all transform hover:scale-105 shadow-lg border-2 border-blue-400"
              >
                🔑 INICIAR SESIÓN
              </a>
            </div>
          </nav>
        </header>

        {/* Hero Section */}
        <section className="relative py-20 px-6 overflow-hidden">
          {/* Gráfica de Trading de Fondo */}
          <div className="absolute inset-0 opacity-20">
            <div className="w-full h-full flex items-center justify-center">
              <svg width="1200" height="600" viewBox="0 0 1200 600" className="w-full h-full">
                {/* Grid de fondo */}
                <defs>
                  <pattern id="grid" width="40" height="40" patternUnits="userSpaceOnUse">
                    <path d="M 40 0 L 0 0 0 40" fill="none" stroke="#ffffff" strokeWidth="0.8" opacity="0.5"/>
                  </pattern>
                </defs>
                <rect width="100%" height="100%" fill="url(#grid)" />

                {/* Líneas de precio principales */}
                <line x1="0" y1="150" x2="1200" y2="150" stroke="#00ff88" strokeWidth="2" opacity="0.8"/>
                <line x1="0" y1="300" x2="1200" y2="300" stroke="#ffffff" strokeWidth="2" opacity="0.6"/>
                <line x1="0" y1="450" x2="1200" y2="450" stroke="#ff4444" strokeWidth="2" opacity="0.8"/>

                {/* Candlesticks simulados */}
                {[...Array(30)].map((_, i) => {
                  const x = 40 + i * 38;
                  const isGreen = Math.random() > 0.4;
                  const high = 100 + Math.random() * 400;
                  const low = high + 20 + Math.random() * 100;
                  const open = high + Math.random() * (low - high);
                  const close = high + Math.random() * (low - high);
                  const bodyTop = Math.min(open, close);
                  const bodyBottom = Math.max(open, close);

                  return (
                    <g key={i}>
                      {/* Mecha superior */}
                      <line
                        x1={x} y1={high} x2={x} y2={bodyTop}
                        stroke={isGreen ? "#00ff88" : "#ff4444"}
                        strokeWidth="2"
                        opacity="0.9"
                      />
                      {/* Cuerpo */}
                      <rect
                        x={x-8} y={bodyTop} width="16" height={bodyBottom - bodyTop}
                        fill={isGreen ? "#00ff88" : "#ff4444"}
                        opacity="0.85"
                      />
                      {/* Mecha inferior */}
                      <line
                        x1={x} y1={bodyBottom} x2={x} y2={low}
                        stroke={isGreen ? "#00ff88" : "#ff4444"}
                        strokeWidth="2"
                        opacity="0.9"
                      />
                    </g>
                  );
                })}

                {/* Línea de tendencia */}
                <path
                  d="M 100 500 Q 300 400 500 350 T 900 200 L 1100 150"
                  fill="none"
                  stroke="#ffa500"
                  strokeWidth="3"
                  opacity="0.8"
                />

                {/* Zonas de liquidez */}
                <rect x="200" y="180" width="300" height="40" fill="#00ff88" opacity="0.2" rx="5"/>
                <rect x="600" y="380" width="250" height="35" fill="#ff4444" opacity="0.2" rx="5"/>

                {/* Indicadores técnicos simulados */}
                <circle cx="300" cy="200" r="5" fill="#ffa500" opacity="0.9"/>
                <circle cx="500" cy="350" r="5" fill="#ffa500" opacity="0.9"/>
                <circle cx="800" cy="250" r="5" fill="#ffa500" opacity="0.9"/>
              </svg>
            </div>
          </div>

          <div className="w-full px-4 relative z-10">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-0 items-center">

              {/* Contenido Principal */}
              <div className="space-y-8 lg:pl-0 lg:ml-0">
                <div className="space-y-6">
                  <h2 className="text-6xl lg:text-8xl font-black leading-tight animate-pulse">
                    <span className="bg-gradient-to-r from-orange-400 via-red-500 to-pink-500 bg-clip-text text-transparent drop-shadow-2xl">
                      GERMAYORI
                    </span>
                  </h2>
                  <h3 className="text-3xl lg:text-4xl font-bold text-white drop-shadow-lg">
                    🏦 Estrategia de Liquidez Institucional
                  </h3>
                  <p className="text-2xl text-gray-200 leading-relaxed font-semibold">
                    💰 Descubre los secretos del trading institucional. Aprende a seguir la liquidez de los grandes bancos y opera como un profesional.
                  </p>

                  {/* NUEVO: Destacar beneficios principales */}
                  <div className="bg-gradient-to-r from-green-500 to-emerald-600 p-6 rounded-2xl border-2 border-green-400 shadow-2xl">
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-white">
                      <div className="flex items-center space-x-3">
                        <span className="text-2xl">✅</span>
                        <span className="font-bold text-lg">Sin Order Blocks tradicionales</span>
                      </div>
                      <div className="flex items-center space-x-3">
                        <span className="text-2xl">🎯</span>
                        <span className="font-bold text-lg">85% Win Rate comprobado</span>
                      </div>
                      <div className="flex items-center space-x-3">
                        <span className="text-2xl">🚀</span>
                        <span className="font-bold text-lg">Acceso inmediato 24/7</span>
                      </div>
                      <div className="flex items-center space-x-3">
                        <span className="text-2xl">💎</span>
                        <span className="font-bold text-lg">Solo $75 USD - Pago único</span>
                      </div>
                    </div>
                  </div>
                </div>

                {/* Estadísticas Impresionantes MEJORADAS */}
                <div className="grid grid-cols-3 gap-6">
                  <div className="text-center bg-gradient-to-br from-green-800 to-green-900 p-6 rounded-2xl border-2 border-green-400 shadow-2xl transform hover:scale-105 transition-all">
                    <div className="text-5xl font-black text-green-300 mb-2 animate-pulse">85%</div>
                    <div className="text-lg font-bold text-white">Win Rate</div>
                    <div className="text-sm text-green-200">Comprobado</div>
                  </div>
                  <div className="text-center bg-gradient-to-br from-blue-800 to-blue-900 p-6 rounded-2xl border-2 border-blue-400 shadow-2xl transform hover:scale-105 transition-all">
                    <div className="text-5xl font-black text-blue-300 mb-2 animate-pulse">2,500+</div>
                    <div className="text-lg font-bold text-white">Pips Ganados</div>
                    <div className="text-sm text-blue-200">Este mes</div>
                  </div>
                  <div className="text-center bg-gradient-to-br from-orange-800 to-red-900 p-6 rounded-2xl border-2 border-orange-400 shadow-2xl transform hover:scale-105 transition-all">
                    <div className="text-5xl font-black text-orange-300 mb-2 animate-pulse">300%</div>
                    <div className="text-lg font-bold text-white">ROI Promedio</div>
                    <div className="text-sm text-orange-200">En 6 meses</div>
                  </div>
                </div>

                {/* Botones CTA MEJORADOS */}
                <div className="flex flex-col sm:flex-row gap-6">
                  <a
                    href="#registro"
                    className="bg-gradient-to-r from-orange-500 via-red-500 to-pink-600 hover:from-orange-600 hover:via-red-600 hover:to-pink-700 text-white px-10 py-6 rounded-2xl font-black text-2xl transition-all transform hover:scale-110 text-center shadow-2xl border-2 border-orange-400 animate-pulse"
                  >
                    🚀 ACCEDER AHORA - $75 USD
                  </a>
                  <a
                    href="#estrategia"
                    className="border-4 border-orange-500 bg-gradient-to-r from-orange-100 to-red-100 text-orange-600 hover:bg-gradient-to-r hover:from-orange-500 hover:to-red-500 hover:text-white px-10 py-6 rounded-2xl font-black text-2xl transition-all transform hover:scale-110 text-center shadow-2xl"
                  >
                    📊 VER ESTRATEGIA
                  </a>
                </div>
              </div>

              {/* Imagen de Germayori MEJORADA */}
              <div className="relative lg:pr-8">
                <div className="relative z-10 bg-gradient-to-br from-blue-800 via-purple-800 to-indigo-900 rounded-3xl p-10 shadow-2xl border-4 border-orange-400 transform hover:scale-105 transition-all">
                  <div className="text-center mb-8">
                    <div className="w-40 h-40 mx-auto mb-6 rounded-full overflow-hidden border-6 border-orange-400 shadow-2xl animate-pulse">
                      <img
                        src="/logo.png"
                        alt="Germayori - Trader Institucional"
                        className="w-full h-full object-cover"
                      />
                    </div>
                    <h4 className="text-3xl font-black text-white mb-2">🏆 GERMAYORI</h4>
                    <p className="text-xl text-orange-300 font-bold">Trader Institucional Profesional</p>
                    <div className="mt-4 bg-gradient-to-r from-green-500 to-emerald-600 p-3 rounded-xl">
                      <p className="text-white font-bold">🔥 +1,247 Traders Exitosos</p>
                    </div>
                  </div>

                  {/* Gráfica de Trading Simulada MEJORADA */}
                  <div className="bg-black rounded-2xl p-6 mb-6 border-2 border-green-400 shadow-2xl">
                    <div className="flex justify-between text-lg mb-4">
                      <span className="text-green-400 font-bold">📈 XAUUSD</span>
                      <span className="text-green-400 font-bold animate-pulse">+2.45%</span>
                    </div>
                    <div className="h-32 flex items-end space-x-2">
                      {[...Array(15)].map((_, i) => (
                        <div
                          key={i}
                          className={`w-4 bg-gradient-to-t ${
                            Math.random() > 0.4 ? 'from-green-400 to-green-600' : 'from-red-400 to-red-600'
                          } rounded-t shadow-lg`}
                          style={{ height: `${Math.random() * 80 + 20}%` }}
                        ></div>
                      ))}
                    </div>
                    <div className="mt-4 text-center">
                      <div className="text-xs text-gray-400">Estrategia Germayori en acción</div>
                    </div>
                  </div>

                  <div className="text-center bg-gradient-to-r from-green-500 to-emerald-600 p-4 rounded-2xl">
                    <div className="text-white font-black text-2xl animate-pulse">💰 +$1,250 HOY</div>
                    <div className="text-green-100 text-lg font-bold">Ganancia en vivo</div>
                    <div className="text-green-200 text-sm">🔥 Liquidez institucional funcionando</div>
                  </div>
                </div>

                {/* Efectos de fondo */}
                <div className="absolute -top-4 -right-4 w-72 h-72 bg-orange-500 rounded-full mix-blend-multiply filter blur-xl opacity-20 animate-pulse"></div>
                <div className="absolute -bottom-4 -left-4 w-72 h-72 bg-blue-500 rounded-full mix-blend-multiply filter blur-xl opacity-20 animate-pulse"></div>
              </div>
            </div>
          </div>
        </section>

        {/* Sección de Estrategia */}
        <section id="estrategia" className="py-20 px-6 bg-black bg-opacity-20">
          <div className="max-w-7xl mx-auto">
            <div className="text-center mb-16">
              <h2 className="text-4xl lg:text-5xl font-bold mb-4">
                <span className="bg-gradient-to-r from-blue-400 to-purple-500 bg-clip-text text-transparent">
                  La Estrategia GERMAYORI
                </span>
              </h2>
              <p className="text-xl text-gray-400 max-w-3xl mx-auto">
                No más Order Blocks tradicionales. Descubre cómo los bancos realmente mueven el mercado.
              </p>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
              {/* Liquidez Institucional */}
              <div className="bg-gradient-to-br from-blue-800 to-blue-900 rounded-xl p-8 text-center transform hover:scale-105 transition-all">
                <div className="text-5xl mb-4">🏦</div>
                <h3 className="text-2xl font-bold mb-4">Liquidez Institucional</h3>
                <p className="text-gray-300">
                  Identifica dónde los grandes bancos colocan sus órdenes masivas y síguelos para obtener ganancias consistentes.
                </p>
              </div>

              {/* Fair Value Gaps */}
              <div className="bg-gradient-to-br from-purple-800 to-purple-900 rounded-xl p-8 text-center transform hover:scale-105 transition-all">
                <div className="text-5xl mb-4">📊</div>
                <h3 className="text-2xl font-bold mb-4">Fair Value Gaps</h3>
                <p className="text-gray-300">
                  Aprovecha los desequilibrios del mercado que los algoritmos institucionales crean para maximizar tus entradas.
                </p>
              </div>

              {/* Análisis Multi-Temporal */}
              <div className="bg-gradient-to-br from-orange-800 to-red-900 rounded-xl p-8 text-center transform hover:scale-105 transition-all">
                <div className="text-5xl mb-4">⏰</div>
                <h3 className="text-2xl font-bold mb-4">Multi-Temporalidad</h3>
                <p className="text-gray-300">
                  Analiza 5 temporalidades simultáneamente para confirmar la dirección institucional del mercado.
                </p>
              </div>
            </div>
          </div>
        </section>

        {/* Sección de Resultados */}
        <section id="resultados" className="py-20 px-6">
          <div className="max-w-7xl mx-auto">
            <div className="text-center mb-16">
              <h2 className="text-4xl lg:text-5xl font-bold mb-4">
                <span className="bg-gradient-to-r from-green-400 to-emerald-500 bg-clip-text text-transparent">
                  Resultados Reales
                </span>
              </h2>
              <p className="text-xl text-gray-400">
                Más de 1,000 traders ya están generando ingresos consistentes
              </p>
            </div>

            {/* Gráfica de Rendimiento */}
            <div className="bg-gradient-to-br from-gray-900 to-black rounded-2xl p-8 mb-12">
              <h3 className="text-2xl font-bold mb-6 text-center">📈 Rendimiento Últimos 6 Meses</h3>
              <div className="grid grid-cols-2 md:grid-cols-6 gap-4 mb-8">
                {[
                  { mes: 'Jul', ganancia: 45, altura: '60%' },
                  { mes: 'Ago', ganancia: 67, altura: '80%' },
                  { mes: 'Sep', ganancia: 89, altura: '100%' },
                  { mes: 'Oct', ganancia: 123, altura: '90%' },
                  { mes: 'Nov', ganancia: 156, altura: '95%' },
                  { mes: 'Dic', ganancia: 189, altura: '85%' }
                ].map((data, index) => (
                  <div key={index} className="text-center">
                    <div className="h-32 flex items-end justify-center mb-2">
                      <div
                        className="w-8 bg-gradient-to-t from-green-400 to-green-600 rounded-t"
                        style={{ height: data.altura }}
                      ></div>
                    </div>
                    <div className="text-sm font-semibold text-green-400">+{data.ganancia}%</div>
                    <div className="text-xs text-gray-400">{data.mes}</div>
                  </div>
                ))}
              </div>

              <div className="grid grid-cols-1 md:grid-cols-3 gap-6 text-center">
                <div className="bg-blue-900 rounded-lg p-4">
                  <div className="text-2xl font-bold text-blue-400">1,247</div>
                  <div className="text-gray-400">Traders Activos</div>
                </div>
                <div className="bg-green-900 rounded-lg p-4">
                  <div className="text-2xl font-bold text-green-400">$2.4M</div>
                  <div className="text-gray-400">Ganancias Generadas</div>
                </div>
                <div className="bg-orange-900 rounded-lg p-4">
                  <div className="text-2xl font-bold text-orange-400">4.8/5</div>
                  <div className="text-gray-400">Calificación</div>
                </div>
              </div>
            </div>

            {/* Testimonios */}
            <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
              {[
                {
                  nombre: "Carlos M.",
                  pais: "🇲🇽 México",
                  testimonio: "En 3 meses pasé de $500 a $2,100. La estrategia de liquidez es increíble.",
                  ganancia: "+320%"
                },
                {
                  nombre: "Ana R.",
                  pais: "🇨🇴 Colombia",
                  testimonio: "Nunca había visto algo tan preciso. Los FVG realmente funcionan.",
                  ganancia: "+280%"
                },
                {
                  nombre: "Luis P.",
                  pais: "🇪🇸 España",
                  testimonio: "Germayori cambió mi vida. Ahora trading es mi trabajo de tiempo completo.",
                  ganancia: "+450%"
                }
              ].map((testimonio, index) => (
                <div key={index} className="bg-gradient-to-br from-blue-800 to-purple-800 rounded-xl p-6">
                  <div className="flex items-center mb-4">
                    <div className="w-12 h-12 bg-gradient-to-br from-orange-400 to-red-500 rounded-full flex items-center justify-center font-bold mr-4">
                      {testimonio.nombre.charAt(0)}
                    </div>
                    <div>
                      <div className="font-semibold">{testimonio.nombre}</div>
                      <div className="text-sm text-gray-400">{testimonio.pais}</div>
                    </div>
                    <div className="ml-auto text-green-400 font-bold">{testimonio.ganancia}</div>
                  </div>
                  <p className="text-gray-300 italic">"{testimonio.testimonio}"</p>
                </div>
              ))}
            </div>
          </div>
        </section>

        {/* Sección de Registro */}
        <section id="registro" className="py-20 px-6 bg-gradient-to-br from-orange-900 to-red-900">
          <div className="max-w-4xl mx-auto">
            <div className="text-center mb-12">
              <h2 className="text-4xl lg:text-5xl font-bold mb-4">
                🚀 Únete a GERMAYORI
              </h2>
              <p className="text-xl text-orange-100">
                Acceso completo por solo $75 USD. Pago único con Yappy.
              </p>
            </div>

            <div className="bg-white rounded-2xl p-8 text-gray-900">
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">

                {/* Formulario de Registro */}
                <div>
                  <h3 className="text-2xl font-bold mb-6 text-gray-900">📝 Registro Rápido</h3>
                  {/* Enlace a Login */}
                  <div className="text-center mb-6">
                    <p className="text-gray-600 mb-3">¿Ya tienes cuenta?</p>
                    <a
                      href="/login"
                      className="inline-block bg-blue-100 hover:bg-blue-200 text-blue-800 px-6 py-3 rounded-lg font-semibold transition-all"
                    >
                      🔑 Iniciar Sesión
                    </a>
                  </div>

                  {/* FORMULARIO CON BORDE REDONDEADO Y COLOR */}
                  <div className="bg-gradient-to-br from-blue-50 to-indigo-100 p-8 rounded-2xl border-4 border-blue-400 shadow-2xl">
                    <form onSubmit={handleRegistro} className="space-y-6">
                      <div>
                        <label className="block text-sm font-bold mb-3 text-blue-800">Nombre Completo</label>
                        <input
                          type="text"
                          name="nombre"
                          value={formData.nombre}
                          onChange={handleInputChange}
                          className="w-full p-4 border-3 border-blue-300 rounded-xl focus:border-orange-500 focus:outline-none focus:ring-2 focus:ring-orange-200 text-lg font-semibold bg-white shadow-lg"
                          placeholder="Tu nombre completo"
                          required
                        />
                      </div>
                      <div>
                        <label className="block text-sm font-bold mb-3 text-blue-800">Email</label>
                        <input
                          type="email"
                          name="email"
                          value={formData.email}
                          onChange={handleInputChange}
                          className="w-full p-4 border-3 border-blue-300 rounded-xl focus:border-orange-500 focus:outline-none focus:ring-2 focus:ring-orange-200 text-lg font-semibold bg-white shadow-lg"
                          placeholder="<EMAIL>"
                          required
                        />
                      </div>
                      <div>
                        <label className="block text-sm font-bold mb-3 text-blue-800">Contraseña</label>
                        <input
                          type="password"
                          name="password"
                          value={formData.password}
                          onChange={handleInputChange}
                          className="w-full p-4 border-3 border-blue-300 rounded-xl focus:border-orange-500 focus:outline-none focus:ring-2 focus:ring-orange-200 text-lg font-semibold bg-white shadow-lg"
                          placeholder="Crea una contraseña segura"
                          required
                          minLength="6"
                        />
                      </div>
                      <div>
                        <label className="block text-sm font-bold mb-3 text-blue-800">Teléfono</label>
                        <input
                          type="tel"
                          name="telefono"
                          value={formData.telefono}
                          onChange={handleInputChange}
                          className="w-full p-4 border-3 border-blue-300 rounded-xl focus:border-orange-500 focus:outline-none focus:ring-2 focus:ring-orange-200 text-lg font-semibold bg-white shadow-lg"
                          placeholder="+507 1234-5678"
                          required
                        />
                      </div>
                      <div>
                        <label className="block text-sm font-bold mb-3 text-blue-800">Edad</label>
                        <input
                          type="number"
                          name="edad"
                          value={formData.edad}
                          onChange={handleInputChange}
                          className="w-full p-4 border-3 border-blue-300 rounded-xl focus:border-orange-500 focus:outline-none focus:ring-2 focus:ring-orange-200 text-lg font-semibold bg-white shadow-lg"
                          placeholder="Tu edad"
                          min="18"
                          max="100"
                          required
                        />
                      </div>
                      <div>
                        <label className="block text-sm font-bold mb-3 text-blue-800">País</label>
                        <select
                          name="pais"
                          value={formData.pais}
                          onChange={handleInputChange}
                          className="w-full p-4 border-3 border-blue-300 rounded-xl focus:border-orange-500 focus:outline-none focus:ring-2 focus:ring-orange-200 text-lg font-semibold bg-white shadow-lg"
                          required
                        >
                        <option value="">Selecciona tu país</option>
                        <option value="Mexico">🇲🇽 México</option>
                        <option value="Colombia">🇨🇴 Colombia</option>
                        <option value="Panama">🇵🇦 Panamá</option>
                        <option value="Costa Rica">🇨🇷 Costa Rica</option>
                        <option value="Guatemala">🇬🇹 Guatemala</option>
                        <option value="Honduras">🇭🇳 Honduras</option>
                        <option value="El Salvador">🇸🇻 El Salvador</option>
                        <option value="Nicaragua">🇳🇮 Nicaragua</option>
                        <option value="Belice">🇧🇿 Belice</option>
                        <option value="República Dominicana">🇩🇴 República Dominicana</option>
                        <option value="Cuba">🇨🇺 Cuba</option>
                        <option value="Jamaica">🇯🇲 Jamaica</option>
                        <option value="Haiti">🇭🇹 Haití</option>
                        <option value="Puerto Rico">🇵🇷 Puerto Rico</option>
                        <option value="Trinidad y Tobago">🇹🇹 Trinidad y Tobago</option>
                        <option value="Barbados">🇧🇧 Barbados</option>
                        <option value="España">🇪🇸 España</option>
                        <option value="Argentina">🇦🇷 Argentina</option>
                        <option value="Chile">🇨🇱 Chile</option>
                        <option value="Peru">🇵🇪 Perú</option>
                        <option value="Venezuela">🇻🇪 Venezuela</option>
                        <option value="Ecuador">🇪🇨 Ecuador</option>
                        <option value="Bolivia">🇧🇴 Bolivia</option>
                        <option value="Paraguay">🇵🇾 Paraguay</option>
                        <option value="Uruguay">🇺🇾 Uruguay</option>
                        <option value="Brasil">🇧🇷 Brasil</option>
                        <option value="USA">🇺🇸 Estados Unidos</option>
                        <option value="Canada">🇨🇦 Canadá</option>
                        <option value="Otro">🌍 Otro</option>
                        </select>
                      </div>
                      <button
                        type="submit"
                        className="w-full bg-gradient-to-r from-orange-500 to-red-600 hover:from-orange-600 hover:to-red-700 text-white py-5 rounded-xl font-black text-xl transition-all transform hover:scale-105 shadow-2xl border-2 border-orange-400"
                      >
                        💳 Registrarse y Pagar $75 USD
                      </button>
                    </form>
                  </div>
                </div>

                {/* Beneficios Incluidos */}
                <div>
                  <h3 className="text-2xl font-bold mb-6 text-gray-900">✨ Todo Incluido</h3>
                  <div className="space-y-4">
                    {[
                      "🎯 Estrategia completa de liquidez institucional",
                      "📊 Análisis en tiempo real de 5 temporalidades",
                      "💬 Chat educativo con IA Germayori",
                      "📡 Señales de trading en vivo",
                      "📈 Acceso a TradingView profesional",
                      "🧮 Calculadora avanzada de riesgo",
                      "📱 Notificaciones push instantáneas",
                      "🎓 Videos educativos exclusivos",
                      "👨‍🏫 Mentoría directa con Germayori",
                      "💎 Acceso VIP al grupo de WhatsApp"
                    ].map((beneficio, index) => (
                      <div key={index} className="flex items-center space-x-3">
                        <div className="w-6 h-6 bg-green-500 rounded-full flex items-center justify-center">
                          <span className="text-white text-xs">✓</span>
                        </div>
                        <span className="text-gray-700">{beneficio}</span>
                      </div>
                    ))}
                  </div>

                  <div className="mt-8 p-4 bg-green-100 rounded-lg">
                    <div className="text-center">
                      <div className="text-2xl font-bold text-green-700">💰 Garantía 30 Días</div>
                      <div className="text-sm text-green-600">Si no estás satisfecho, te devolvemos tu dinero</div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </section>

        {/* Sección de Pago con Yappy */}
        {mostrarPago && (
          <section id="pago-yappy" className="py-20 px-6 bg-gradient-to-br from-green-900 to-blue-900">
            <div className="max-w-4xl mx-auto">
              <div className="text-center mb-12">
                <h2 className="text-4xl lg:text-5xl font-bold mb-4">
                  🎉 ¡Registro Exitoso!
                </h2>
                <p className="text-xl text-green-100 mb-2">
                  Hola <strong>{datosUsuario?.nombre || 'Usuario'}</strong>, tu cuenta ha sido creada.
                </p>
                <p className="text-lg text-blue-100">
                  Ahora completa tu pago de <strong>$75 USD</strong> para activar tu acceso.
                </p>
              </div>

              <div className="bg-white rounded-2xl p-8 text-gray-900">
                <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">

                  {/* Código QR de Yappy */}
                  <div className="text-center">
                    <h3 className="text-2xl font-bold mb-6 text-gray-900">💳 Pagar con Yappy</h3>
                    <div className="bg-gradient-to-br from-blue-50 to-orange-50 rounded-xl p-6 mb-6 border-2 border-orange-200">
                      {/* Logo de Yappy */}
                      <div className="text-center mb-4">
                        <div className="flex items-center justify-center space-x-2 mb-2">
                          <div className="w-8 h-8 bg-blue-500 rounded-full"></div>
                          <div className="w-6 h-6 bg-orange-500 rounded-full"></div>
                        </div>
                        <h4 className="text-2xl font-bold text-blue-600">yappy</h4>
                        <p className="text-gray-600 text-sm">¡Paga fácil y rápido aquí usando Yappy!</p>
                      </div>

                      {/* Código QR REAL de Yappy - SIN DISTORSIÓN */}
                      <div className="w-full mx-auto mb-8 bg-white rounded-xl p-6 shadow-2xl border-4 border-green-400" style={{ maxWidth: '600px' }}>
                        <img
                          src="/qr.png"
                          alt="Código QR Yappy Real - GERMAYORI @Runningpip $75 USD"
                          className="w-full h-auto rounded-xl cursor-pointer hover:scale-105 transition-transform"
                          onClick={() => window.open('/qr.png', '_blank')}
                        />
                        <div className="text-center mt-6 bg-green-100 p-8 rounded-lg">
                          <p className="text-4xl font-black text-green-700 mb-4">
                            📱 ESCANEA ESTE CÓDIGO QR CON YAPPY
                          </p>
                          <p className="text-3xl text-green-600 font-black mb-4">
                            💰 PAGA $75.00 USD A @Runningpip
                          </p>
                          <p className="text-2xl text-gray-700 font-bold">
                            👆 Haz clic para ampliar
                          </p>
                        </div>
                      </div>

                      {/* Información del destinatario */}
                      <div className="text-center bg-white rounded-lg p-4 shadow-sm">
                        <p className="text-xl font-bold text-gray-800">GERMAYORI</p>
                        <p className="text-gray-600">@Runningpip</p>
                        <div className="mt-3 p-2 bg-green-100 rounded-lg">
                          <p className="text-2xl font-bold text-green-700">$75.00 USD</p>
                          <p className="text-sm text-green-600">Monto exacto a pagar</p>
                        </div>
                      </div>
                    </div>
                  </div>

                  {/* Instrucciones */}
                  <div>
                    <h3 className="text-4xl font-black mb-8 text-green-800">📋 Instrucciones</h3>
                    <div className="space-y-6">
                      <div className="flex items-start space-x-4 p-6 bg-white border-2 border-green-300 rounded-lg shadow-sm">
                        <div className="bg-green-600 text-white rounded-full w-12 h-12 flex items-center justify-center font-bold text-xl">1</div>
                        <div>
                          <p className="text-green-800 font-black text-2xl mb-2">Abre tu app de Yappy</p>
                          <p className="text-gray-800 text-lg">Escanea el código QR y paga exactamente <strong className="text-green-600">$75.00 USD</strong> a <strong>@Runningpip</strong></p>
                        </div>
                      </div>
                      <div className="flex items-start space-x-4 p-6 bg-white border-2 border-green-300 rounded-lg shadow-sm">
                        <div className="bg-green-600 text-white rounded-full w-12 h-12 flex items-center justify-center font-bold text-xl">2</div>
                        <div>
                          <p className="text-green-800 font-black text-2xl mb-2">Captura el comprobante</p>
                          <p className="text-gray-800 text-lg">Toma screenshot del comprobante que muestra el pago exitoso</p>
                        </div>
                      </div>
                      <div className="flex items-start space-x-4 p-6 bg-white border-2 border-green-300 rounded-lg shadow-sm">
                        <div className="bg-green-600 text-white rounded-full w-12 h-12 flex items-center justify-center font-bold text-xl">3</div>
                        <div>
                          <p className="text-green-800 font-black text-2xl mb-2">Únete al grupo VIP</p>
                          <p className="text-gray-800 text-lg">Haz clic en el botón de abajo y envía tu captura al grupo de WhatsApp</p>
                        </div>
                      </div>
                      <div className="flex items-start space-x-4 p-6 bg-white border-2 border-green-300 rounded-lg shadow-sm">
                        <div className="bg-green-600 text-white rounded-full w-12 h-12 flex items-center justify-center font-bold text-xl">4</div>
                        <div>
                          <p className="text-green-800 font-black text-2xl mb-2">Activación manual</p>
                          <p className="text-gray-800 text-lg">Los administradores verificarán tu pago y activarán tu acceso en máximo 24 horas</p>
                        </div>
                      </div>
                    </div>

                    {/* Botón del Grupo WhatsApp */}
                    <div className="mt-8">
                      <a
                        href="https://chat.whatsapp.com/L4OdlXIE4Kx3av3TSQVOS6"
                        target="_blank"
                        rel="noopener noreferrer"
                        className="w-full bg-gradient-to-r from-green-500 to-green-600 hover:from-green-600 hover:to-green-700 text-white py-4 px-6 rounded-xl font-bold text-lg transition-all transform hover:scale-105 flex items-center justify-center space-x-3 shadow-lg"
                      >
                        <svg className="w-6 h-6" fill="currentColor" viewBox="0 0 24 24">
                          <path d="M17.472 14.382c-.297-.149-1.758-.867-2.03-.967-.273-.099-.471-.148-.67.15-.197.297-.767.966-.94 1.164-.173.199-.347.223-.644.075-.297-.15-1.255-.463-2.39-1.475-.883-.788-1.48-1.761-1.653-2.059-.173-.297-.018-.458.13-.606.134-.133.298-.347.446-.52.149-.174.198-.298.298-.497.099-.198.05-.371-.025-.52-.075-.149-.669-1.612-.916-2.207-.242-.579-.487-.5-.669-.51-.173-.008-.371-.01-.57-.01-.198 0-.52.074-.792.372-.272.297-1.04 1.016-1.04 2.479 0 1.462 1.065 2.875 1.213 3.074.149.198 2.096 3.2 5.077 4.487.709.306 1.262.489 1.694.625.712.227 1.36.195 1.871.118.571-.085 1.758-.719 2.006-1.413.248-.694.248-1.289.173-1.413-.074-.124-.272-.198-.57-.347m-5.421 7.403h-.004a9.87 9.87 0 01-5.031-1.378l-.361-.214-3.741.982.998-3.648-.235-.374a9.86 9.86 0 01-1.51-5.26c.001-5.45 4.436-9.884 9.888-9.884 2.64 0 5.122 1.03 6.988 2.898a9.825 9.825 0 012.893 6.994c-.003 5.45-4.437 9.884-9.885 9.884m8.413-18.297A11.815 11.815 0 0012.05 0C5.495 0 .16 5.335.157 11.892c0 2.096.547 4.142 1.588 5.945L.057 24l6.305-1.654a11.882 11.882 0 005.683 1.448h.005c6.554 0 11.89-5.335 11.893-11.893A11.821 11.821 0 0020.885 3.787"/>
                        </svg>
                        <span>Unirse al Grupo VIP de WhatsApp</span>
                      </a>
                      <p className="text-center text-sm text-gray-600 mt-2">
                        👆 Haz clic aquí después de realizar el pago
                      </p>
                    </div>

                    {/* Información Adicional */}
                    <div className="mt-8 grid grid-cols-1 md:grid-cols-2 gap-6">
                      <div className="p-6 bg-green-50 rounded-lg border-l-4 border-green-500">
                        <h4 className="font-bold text-green-800 mb-3 text-lg">📧 Tu Información</h4>
                        <p className="text-base text-green-700">
                          <strong>Email:</strong> {datosUsuario?.email || 'No disponible'}<br/>
                          <strong>Nombre:</strong> {datosUsuario?.nombre || 'No disponible'}
                        </p>
                      </div>
                      <div className="p-6 bg-green-50 rounded-lg border-l-4 border-green-500">
                        <h4 className="font-bold text-green-800 mb-3 text-lg">⏰ Detalles del Acceso</h4>
                        <p className="text-base text-green-700">
                          <strong>Duración:</strong> 30 días<br/>
                          <strong>Estado:</strong> <span className="text-green-600 font-bold">Pendiente de pago</span>
                        </p>
                      </div>
                    </div>

                    {/* Nota importante */}
                    <div className="mt-8 p-6 bg-green-50 border-2 border-green-300 rounded-lg">
                      <div className="flex items-start space-x-3">
                        <span className="text-green-600 text-2xl">⚠️</span>
                        <div>
                          <h4 className="font-bold text-green-800 text-lg mb-2">Importante:</h4>
                          <p className="text-base text-green-700">
                            Asegúrate de pagar exactamente <strong className="text-green-800">$75.00 USD</strong>. Pagos con montos diferentes pueden causar retrasos en la activación.
                          </p>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </section>
        )}

        {/* Footer */}
        <footer className="py-12 px-6 bg-black">
          <div className="max-w-7xl mx-auto text-center">
            <div className="text-center mb-4">
              <h3 className="text-2xl font-bold">GERMAYORI</h3>
            </div>
            <p className="text-gray-400 mb-4">
              La estrategia de trading más avanzada del mercado
            </p>
            <div className="flex justify-center space-x-6 text-sm text-gray-500">
              <span>© 2024 Germayori</span>
              <span>•</span>
              <span>Términos y Condiciones</span>
              <span>•</span>
              <span>Política de Privacidad</span>
            </div>
          </div>
        </footer>

      </div>
    </>
  );
}
