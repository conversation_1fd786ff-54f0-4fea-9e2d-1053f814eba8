import React from "react";

export default function CanalPago() {
  const planes = [
    {
      nombre: "🔥 Plan VIP Mensual",
      precio: "$49",
      beneficios: [
        "Acceso al canal de señales",
        "Análisis institucional diario",
        "Clases grabadas y en vivo",
        "Atención personalizada",
      ],
      metodo: "https://wa.me/50760000000?text=Hola%20quiero%20pagar%20el%20plan%20VIP",
    },
    {
      nombre: "💎 Plan Élite Trimestral",
      precio: "$129",
      beneficios: [
        "Todo lo del plan VIP",
        "Sesión privada mensual",
        "Evaluación de progreso",
        "Descuento en fondeo",
      ],
      metodo: "https://wa.me/50760000000?text=Hola%20quiero%20el%20plan%20élite%20trimestral",
    },
  ];

  return (
    <div className="min-h-screen bg-gray-900 text-white p-6">
      <h1 className="text-3xl font-bold text-center mb-6">💳 Suscripciones y Métodos de Pago</h1>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-6 max-w-4xl mx-auto">
        {planes.map((plan, index) => (
          <div key={index} className="bg-gray-800 p-6 rounded-lg shadow-md">
            <h2 className="text-xl font-semibold mb-2">{plan.nombre}</h2>
            <p className="text-green-400 text-lg mb-2">{plan.precio}</p>
            <ul className="list-disc list-inside text-gray-300 mb-4">
              {plan.beneficios.map((b, i) => (
                <li key={i}>{b}</li>
              ))}
            </ul>
            <a
              href={plan.metodo}
              target="_blank"
              rel="noopener noreferrer"
              className="inline-block bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded"
            >
              💸 Pagar por WhatsApp
            </a>
          </div>
        ))}
      </div>
    </div>
  );
}
