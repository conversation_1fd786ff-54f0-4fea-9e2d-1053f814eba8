import React from "react";

export default function CanalMentoria() {
  const sesiones = [
    {
      titulo: "📆 Mentoría en vivo - Smart Money y Liquidez",
      fecha: "Viernes 14 de junio, 7:00 PM",
      enlace: "https://zoom.us/j/1234567890",
    },
    {
      titulo: "📚 Taller de Gestión de Capital",
      fecha: "Domingo 16 de junio, 6:00 PM",
      enlace: "https://zoom.us/j/9876543210",
    },
  ];

  return (
    <div className="min-h-screen bg-gray-900 text-white p-6">
      <h1 className="text-3xl font-bold text-center mb-6">📅 Mentorías y Clases en Vivo</h1>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-6 max-w-4xl mx-auto">
        {sesiones.map((s, index) => (
          <div key={index} className="bg-gray-800 p-4 rounded-lg shadow-md">
            <h2 className="text-xl font-semibold mb-2">{s.titulo}</h2>
            <p className="mb-2 text-gray-300">{s.fecha}</p>
            <a
              href={s.enlace}
              target="_blank"
              rel="noopener noreferrer"
              className="inline-block bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded"
            >
              🔗 Unirse por Zoom
            </a>
          </div>
        ))}
      </div>
    </div>
  );
}
