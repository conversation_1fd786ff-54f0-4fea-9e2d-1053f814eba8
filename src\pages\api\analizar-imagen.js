const formidable = require("formidable");
const fs = require("fs");
const OpenAI = require("openai");

const openai = new OpenAI({ apiKey: process.env.OPENAI_API_KEY });

export const config = {
  api: {
    bodyParser: false,
  },
};

export default async function handler(req, res) {
  if (req.method !== "POST") {
    return res.status(405).json({ error: "Método no permitido" });
  }

  const form = new formidable.IncomingForm({ multiples: true });

  form.parse(req, async (err, fields, files) => {
    if (err) {
      console.error("Error al parsear:", err);
      return res.status(500).json({ error: "Error al procesar las imágenes." });
    }

    // Manejar múltiples archivos
    const fileKeys = ['diario', 'h4', 'h1', 'min30', 'min5'];
    const imageContents = [];

    for (const key of fileKeys) {
      if (files[key]) {
        const file = Array.isArray(files[key]) ? files[key][0] : files[key];
        const imageBuffer = fs.readFileSync(file.filepath);
        const base64Image = imageBuffer.toString("base64");
        imageContents.push({
          timeframe: key,
          image: base64Image
        });
      }
    }

    if (imageContents.length === 0) {
      return res.status(400).json({ error: "No se recibieron imágenes." });
    }

    try {
      // Crear el contenido del mensaje con múltiples imágenes
      const userContent = [
        {
          type: "text",
          text: `Analiza estas ${imageContents.length} imágenes de diferentes temporalidades para dar una señal de trading usando la Estrategia de Liquidez Germayori. Las temporalidades son: ${imageContents.map(img => img.timeframe).join(', ')}.`
        }
      ];

      // Agregar cada imagen al contenido
      imageContents.forEach((imgData, index) => {
        userContent.push({
          type: "image_url",
          image_url: {
            url: `data:image/png;base64,${imgData.image}`,
          },
        });
      });

      const response = await openai.chat.completions.create({
        model: "gpt-4o",
        temperature: 0.7,
        max_tokens: 1000,
        messages: [
          {
            role: "system",
            content: `Eres GERMAYORI, experto en trading institucional. Usas la Estrategia de Liquidez Germayori (institucional refinada):

📊 ESTRATEGIA DE LIQUIDEZ GERMAYORI:

🔻 Liquidez Inferior (bajo importante):
Si el precio liquida un bajo importante, se busca una COMPRA.

🔺 Liquidez Superior (alto importante):
Si el precio liquida un alto importante, se busca una VENTA.

📉 Esperar Retroceso a Fair Value Gap (FVG):
Luego de la liquidación de liquidez, no se entra de inmediato. Se espera un retroceso claro hacia un FVG para tomar una entrada más precisa.

🏦 Confirmación con Estrategia Institucional:
Además, se analiza la acción del precio con lógica institucional (flujo de órdenes bancarias). Se considera:
- Desequilibrios
- Expansiones y retrocesos
- Impulsos alineados con la liquidez ya tomada

🚫 Evitar Order Blocks tradicionales:
No se usan bloques de órdenes simples. Pueden fallar y no son confiables por sí solos.

FORMATO DE RESPUESTA:
🟢 COMPRA o 🔴 VENTA en [PAR]
📍 Entrada: [precio]
⛔ SL: [stop loss]
🎯 TP1: [precio] | TP2: [precio] | TP3: [precio]
🧠 Justificación: [Explica usando liquidez, FVG, desequilibrios y manipulación institucional. NUNCA menciones soporte ni resistencia]`,
          },
          {
            role: "user",
            content: userContent,
          },
        ],
      });

      return res.status(200).json({ resultado: response.choices[0].message.content });
    } catch (error) {
      console.error("Error al llamar a OpenAI:", error);
      return res.status(500).json({ error: "Error al generar la señal." });
    }
  });
}
