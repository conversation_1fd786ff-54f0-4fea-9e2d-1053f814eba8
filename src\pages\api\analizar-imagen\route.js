import { OpenAI } from 'openai';
import formidable from 'formidable';
import fs from 'fs';

export const config = {
  api: {
    bodyParser: false,
  },
};

export async function POST(req) {
  const form = formidable({ multiples: false });

  return new Promise((resolve, reject) => {
    form.parse(req, async (err, fields, files) => {
      if (err) {
        console.error('❌ Error al procesar el formulario:', err);
        return resolve(Response.json({ success: false, message: 'Error al procesar el formulario', error: err.message }));
      }

      const fileCandidate = Object.values(files)[0];
      const imageFile = Array.isArray(fileCandidate) ? fileCandidate[0] : fileCandidate;

      if (!imageFile) {
        return resolve(Response.json({ success: false, message: 'No se recibió la imagen.' }));
      }

      const base64Image = fs.readFileSync(imageFile.filepath, 'base64');

      const openai = new OpenAI({ apiKey: process.env.OPENAI_API_KEY });

      try {
        const response = await openai.chat.completions.create({
          model: 'gpt-4o',
          messages: [
            {
              role: 'system',
              content: `Eres un experto en trading institucional. Analizas imágenes de gráficas de velas japonesas usando la estrategia Germayori, basada en múltiples temporalidades, estructura del mercado y manipulación institucional. Nunca menciones soporte ni resistencia.`,
            },
            {
              role: 'user',
              content: [
                {
                  type: 'text',
                  text: `Analiza la siguiente imagen de trading y devuelve una señal con este formato exacto:

🟢 COMPRA o 🔴 VENTA  
💱 Par: (ej. XAUUSD)  
📍 Entrada: (precio exacto)  
⛔ SL: (stop loss)  
🎯 TP1: (take profit 1)  
🎯 TP2: (take profit 2)  
🎯 TP3: (take profit 3)  
🧠 Justificación: (explica la entrada sin mencionar soporte ni resistencia, solo acción del precio y manipulación institucional)`,
                },
                {
                  type: 'image_url',
                  image_url: {
                    url: `data:image/png;base64,${base64Image}`,
                  },
                },
              ],
            },
          ],
        });

        const content = response.choices[0].message.content;
        return resolve(Response.json({ success: true, respuesta: content }));
      } catch (error) {
        console.error('❌ Error procesando imagen:', error);
        return resolve(Response.json({
          success: false,
          message: 'Error procesando imagen.',
          error: error.message,
          stack: error.stack,
        }));
      }
    });
  });
}
